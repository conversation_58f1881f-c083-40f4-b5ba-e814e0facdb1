[16:08:49] [main/INFO] (FabricLoader/GameProvider) Loading Minecraft 1.20.1 with Fabric Loader 0.16.13
[16:08:49] [main/DEBUG] (FabricLoader/GamePatch) Found game constructor: net.minecraft.client.main.Main -> net.minecraft.client.MinecraftClient
[16:08:49] [main/DEBUG] (FabricLoader/GamePatch) Patching game constructor <init>(Lnet/minecraft/client/RunArgs;)V
[16:08:49] [main/DEBUG] (FabricLoader/GamePatch) Run directory field is thought to be net/minecraft/client/MinecraftClient/runDirectory
[16:08:49] [main/DEBUG] (FabricLoader/GamePatch) Applying brand name hook to net/minecraft/client/ClientBrandRetriever::getClientModName
[16:08:49] [main/DEBUG] (FabricLoader/GamePatch) Applying brand name hook to net/minecraft/server/MinecraftServer::getServerModName
[16:08:49] [main/DEBUG] (FabricLoader/Mappings) Loading mappings took 153 ms
[16:08:49] [main/DEBUG] (FabricLoader/GamePatch) Patched 3 classs
[16:08:49] [main/DEBUG] (FabricLoader/Discovery) Mod discovery time: 27.3 ms
[16:08:49] [main/DEBUG] (FabricLoader/Resolution) Mod resolution time: 59.1 ms
[16:08:49] [main/INFO] (FabricLoader) Loading 59 mods:
	- fabric-api 0.92.5+1.20.1
	- fabric-api-base 0.4.32+1802ada577
	- fabric-api-lookup-api-v1 1.6.37+1802ada577
	- fabric-biome-api-v1 13.0.14+1802ada577
	- fabric-block-api-v1 1.0.12+1802ada577
	- fabric-block-view-api-v2 1.0.3+924f046a77
	- fabric-blockrenderlayer-v1 1.1.42+1802ada577
	- fabric-client-tags-api-v1 1.1.3+1802ada577
	- fabric-command-api-v1 1.2.35+f71b366f77
	- fabric-command-api-v2 2.2.14+1802ada577
	- fabric-commands-v0 0.2.52+df3654b377
	- fabric-containers-v0 0.1.66+df3654b377
	- fabric-content-registries-v0 4.0.13+1802ada577
	- fabric-convention-tags-v1 1.5.6+1802ada577
	- fabric-crash-report-info-v1 0.2.20+1802ada577
	- fabric-data-attachment-api-v1 1.0.2+de0fd6d177
	- fabric-data-generation-api-v1 12.3.6+1802ada577
	- fabric-dimensions-v1 2.1.55+1802ada577
	- fabric-entity-events-v1 1.6.1+1c78457f77
	- fabric-events-interaction-v0 0.6.4+13a40c6677
	- fabric-events-lifecycle-v0 0.2.64+df3654b377
	- fabric-game-rule-api-v1 1.0.41+1802ada577
	- fabric-gametest-api-v1 1.2.15+1802ada577
	- fabric-item-api-v1 2.1.29+1802ada577
	- fabric-item-group-api-v1 4.0.14+1802ada577
	- fabric-key-binding-api-v1 1.0.38+1802ada577
	- fabric-keybindings-v0 0.2.36+df3654b377
	- fabric-lifecycle-events-v1 2.2.23+1802ada577
	- fabric-loot-api-v2 1.2.3+1802ada577
	- fabric-loot-tables-v1 1.1.47+9e7660c677
	- fabric-message-api-v1 5.1.10+1802ada577
	- fabric-mining-level-api-v1 2.1.52+1802ada577
	- fabric-model-loading-api-v1 1.0.4+1802ada577
	- fabric-models-v0 0.4.3+9386d8a777
	- fabric-networking-api-v1 1.3.13+13a40c6677
	- fabric-networking-v0 0.3.53+df3654b377
	- fabric-object-builder-api-v1 11.1.5+e35120df77
	- fabric-particles-v1 1.1.3+1802ada577
	- fabric-recipe-api-v1 1.0.23+1802ada577
	- fabric-registry-sync-v0 2.3.5+1802ada577
	- fabric-renderer-api-v1 3.2.2+1802ada577
	- fabric-renderer-indigo 1.5.3+85287f9f77
	- fabric-renderer-registries-v1 3.2.47+df3654b377
	- fabric-rendering-data-attachment-v1 0.3.39+92a0d36777
	- fabric-rendering-fluids-v1 3.0.29+1802ada577
	- fabric-rendering-v0 1.1.50+df3654b377
	- fabric-rendering-v1 3.0.9+1802ada577
	- fabric-resource-conditions-api-v1 2.3.9+1802ada577
	- fabric-resource-loader-v0 0.11.12+fb82e9d777
	- fabric-screen-api-v1 2.0.9+1802ada577
	- fabric-screen-handler-api-v1 1.3.32+1802ada577
	- fabric-sound-api-v1 1.0.14+1802ada577
	- fabric-transfer-api-v1 3.3.6+8dd72ea377
	- fabric-transitive-access-wideners-v1 4.3.2+1802ada577
	- fabricloader 0.16.13
	- java 17
	- minecraft 1.20.1
	- mixinextras 0.4.1
	- pokecobbleclaim 1.0.0
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-events-lifecycle-v0/0.2.64+df3654b377/fabric-events-lifecycle-v0-0.2.64+df3654b377.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-convention-tags-v1/1.5.6+1802ada577/fabric-convention-tags-v1-1.5.6+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-dimensions-v1/2.1.55+1802ada577/fabric-dimensions-v1-2.1.55+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-item-group-api-v1/4.0.14+1802ada577/fabric-item-group-api-v1-4.0.14+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-object-builder-api-v1/11.1.5+e35120df77/fabric-object-builder-api-v1-11.1.5+e35120df77.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/build/resources/main to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-block-view-api-v2/1.0.3+924f046a77/fabric-block-view-api-v2-1.0.3+924f046a77.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-lifecycle-events-v1/2.2.23+1802ada577/fabric-lifecycle-events-v1-2.2.23+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-resource-conditions-api-v1/2.3.9+1802ada577/fabric-resource-conditions-api-v1-2.3.9+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-screen-api-v1/2.0.9+1802ada577/fabric-screen-api-v1-2.0.9+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-gametest-api-v1/1.2.15+1802ada577/fabric-gametest-api-v1-1.2.15+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-transitive-access-wideners-v1/4.3.2+1802ada577/fabric-transitive-access-wideners-v1-4.3.2+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-client-tags-api-v1/1.1.3+1802ada577/fabric-client-tags-api-v1-1.1.3+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-rendering-data-attachment-v1/0.3.39+92a0d36777/fabric-rendering-data-attachment-v1-0.3.39+92a0d36777.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-sound-api-v1/1.0.14+1802ada577/fabric-sound-api-v1-1.0.14+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-message-api-v1/5.1.10+1802ada577/fabric-message-api-v1-5.1.10+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-renderer-api-v1/3.2.2+1802ada577/fabric-renderer-api-v1-3.2.2+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-entity-events-v1/1.6.1+1c78457f77/fabric-entity-events-v1-1.6.1+1c78457f77.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-game-rule-api-v1/1.0.41+1802ada577/fabric-game-rule-api-v1-1.0.41+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-api/0.92.5+1.20.1/fabric-api-0.92.5+1.20.1.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-registry-sync-v0/2.3.5+1802ada577/fabric-registry-sync-v0-2.3.5+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-block-api-v1/1.0.12+1802ada577/fabric-block-api-v1-1.0.12+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-rendering-v0/1.1.50+df3654b377/fabric-rendering-v0-1.1.50+df3654b377.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-biome-api-v1/13.0.14+1802ada577/fabric-biome-api-v1-13.0.14+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-keybindings-v0/0.2.36+df3654b377/fabric-keybindings-v0-0.2.36+df3654b377.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-renderer-indigo/1.5.3+85287f9f77/fabric-renderer-indigo-1.5.3+85287f9f77.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-resource-loader-v0/0.11.12+fb82e9d777/fabric-resource-loader-v0-0.11.12+fb82e9d777.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-key-binding-api-v1/1.0.38+1802ada577/fabric-key-binding-api-v1-1.0.38+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-recipe-api-v1/1.0.23+1802ada577/fabric-recipe-api-v1-1.0.23+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-models-v0/0.4.3+9386d8a777/fabric-models-v0-0.4.3+9386d8a777.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-transfer-api-v1/3.3.6+8dd72ea377/fabric-transfer-api-v1-3.3.6+8dd72ea377.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-renderer-registries-v1/3.2.47+df3654b377/fabric-renderer-registries-v1-3.2.47+df3654b377.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-content-registries-v0/4.0.13+1802ada577/fabric-content-registries-v0-4.0.13+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-loot-tables-v1/1.1.47+9e7660c677/fabric-loot-tables-v1-1.1.47+9e7660c677.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-model-loading-api-v1/1.0.4+1802ada577/fabric-model-loading-api-v1-1.0.4+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-networking-api-v1/1.3.13+13a40c6677/fabric-networking-api-v1-1.3.13+13a40c6677.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-data-attachment-api-v1/1.0.2+de0fd6d177/fabric-data-attachment-api-v1-1.0.2+de0fd6d177.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-mining-level-api-v1/2.1.52+1802ada577/fabric-mining-level-api-v1-2.1.52+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-command-api-v1/1.2.35+f71b366f77/fabric-command-api-v1-1.2.35+f71b366f77.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-networking-v0/0.3.53+df3654b377/fabric-networking-v0-0.3.53+df3654b377.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-command-api-v2/2.2.14+1802ada577/fabric-command-api-v2-2.2.14+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-crash-report-info-v1/0.2.20+1802ada577/fabric-crash-report-info-v1-0.2.20+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-api-lookup-api-v1/1.6.37+1802ada577/fabric-api-lookup-api-v1-1.6.37+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-item-api-v1/2.1.29+1802ada577/fabric-item-api-v1-2.1.29+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-blockrenderlayer-v1/1.1.42+1802ada577/fabric-blockrenderlayer-v1-1.1.42+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-loot-api-v2/1.2.3+1802ada577/fabric-loot-api-v2-1.2.3+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-screen-handler-api-v1/1.3.32+1802ada577/fabric-screen-handler-api-v1-1.3.32+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-data-generation-api-v1/12.3.6+1802ada577/fabric-data-generation-api-v1-12.3.6+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-api-base/0.4.32+1802ada577/fabric-api-base-0.4.32+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-particles-v1/1.1.3+1802ada577/fabric-particles-v1-1.1.3+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-events-interaction-v0/0.6.4+13a40c6677/fabric-events-interaction-v0-0.6.4+13a40c6677.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-commands-v0/0.2.52+df3654b377/fabric-commands-v0-0.2.52+df3654b377.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/.gradle/caches/modules-2/files-2.1/io.github.llamalad7/mixinextras-fabric/0.4.1/8d1a9e96afb990367fa1f904d17580d164da72e3/mixinextras-fabric-0.4.1.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-rendering-v1/3.0.9+1802ada577/fabric-rendering-v1-3.0.9+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-rendering-fluids-v1/3.0.29+1802ada577/fabric-rendering-fluids-v1-3.0.29+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-containers-v0/0.1.66+df3654b377/fabric-containers-v0-0.1.66+df3654b377.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Entrypoint) Registering new-style initializer net.fabricmc.fabric.impl.event.lifecycle.v0.client.LegacyClientEventInvokers for mod fabric-events-lifecycle-v0 (key client)
[16:08:49] [main/DEBUG] (FabricLoader/Entrypoint) Registering new-style initializer net.fabricmc.fabric.impl.event.lifecycle.v0.LegacyEventInvokers for mod fabric-events-lifecycle-v0 (key main)
[16:08:49] [main/DEBUG] (FabricLoader/Entrypoint) Registering new-style initializer com.pokecobble.town.server.ServerInitializer for mod pokecobbleclaim (key server)
[16:08:49] [main/DEBUG] (FabricLoader/Entrypoint) Registering new-style initializer com.pokecobble.PokecobbleclaimDataGenerator for mod pokecobbleclaim (key fabric-datagen)
[16:08:49] [main/DEBUG] (FabricLoader/Entrypoint) Registering new-style initializer com.pokecobble.PokecobbleclaimClient for mod pokecobbleclaim (key client)
[16:08:49] [main/DEBUG] (FabricLoader/Entrypoint) Registering new-style initializer com.pokecobble.Pokecobbleclaim for mod pokecobbleclaim (key main)
[16:08:49] [main/DEBUG] (FabricLoader/Entrypoint) Registering new-style initializer net.fabricmc.fabric.impl.client.event.lifecycle.ClientLifecycleEventsImpl for mod fabric-lifecycle-events-v1 (key client)
[16:08:49] [main/DEBUG] (FabricLoader/Entrypoint) Registering new-style initializer net.fabricmc.fabric.impl.event.lifecycle.LifecycleEventsImpl for mod fabric-lifecycle-events-v1 (key main)
[16:08:49] [main/DEBUG] (FabricLoader/Entrypoint) Registering new-style initializer net.fabricmc.fabric.impl.gametest.FabricGameTestModInitializer for mod fabric-gametest-api-v1 (key main)
[16:08:49] [main/DEBUG] (FabricLoader/Entrypoint) Registering new-style initializer net.fabricmc.fabric.impl.client.registry.sync.FabricRegistryClientInit for mod fabric-registry-sync-v0 (key client)
[16:08:49] [main/DEBUG] (FabricLoader/Entrypoint) Registering new-style initializer net.fabricmc.fabric.impl.registry.sync.FabricRegistryInit for mod fabric-registry-sync-v0 (key main)
[16:08:49] [main/DEBUG] (FabricLoader/Entrypoint) Registering new-style initializer net.fabricmc.fabric.impl.client.rendering.v0.RenderingCallbackInvoker for mod fabric-rendering-v0 (key client)
[16:08:49] [main/DEBUG] (FabricLoader/Entrypoint) Registering new-style initializer net.fabricmc.fabric.impl.client.indigo.Indigo for mod fabric-renderer-indigo (key client)
[16:08:49] [main/DEBUG] (FabricLoader/Entrypoint) Registering new-style initializer net.fabricmc.fabric.impl.recipe.ingredient.client.CustomIngredientSyncClient for mod fabric-recipe-api-v1 (key client)
[16:08:49] [main/DEBUG] (FabricLoader/Entrypoint) Registering new-style initializer net.fabricmc.fabric.impl.recipe.ingredient.CustomIngredientInit for mod fabric-recipe-api-v1 (key main)
[16:08:49] [main/DEBUG] (FabricLoader/Entrypoint) Registering new-style initializer net.fabricmc.fabric.impl.recipe.ingredient.CustomIngredientSync for mod fabric-recipe-api-v1 (key main)
[16:08:49] [main/DEBUG] (FabricLoader/Entrypoint) Registering new-style initializer net.fabricmc.fabric.impl.loot.table.LootTablesV1Init for mod fabric-loot-tables-v1 (key main)
[16:08:49] [main/DEBUG] (FabricLoader/Entrypoint) Registering new-style initializer net.fabricmc.fabric.impl.networking.client.ClientNetworkingImpl::clientInit for mod fabric-networking-api-v1 (key client)
[16:08:49] [main/DEBUG] (FabricLoader/Entrypoint) Registering new-style initializer net.fabricmc.fabric.impl.networking.NetworkingImpl::init for mod fabric-networking-api-v1 (key main)
[16:08:49] [main/DEBUG] (FabricLoader/Entrypoint) Registering new-style initializer net.fabricmc.fabric.impl.attachment.AttachmentEntrypoint for mod fabric-data-attachment-api-v1 (key main)
[16:08:49] [main/DEBUG] (FabricLoader/Entrypoint) Registering new-style initializer net.fabricmc.fabric.impl.command.v1.LegacyHandler for mod fabric-command-api-v1 (key main)
[16:08:49] [main/DEBUG] (FabricLoader/Entrypoint) Registering new-style initializer net.fabricmc.fabric.impl.client.networking.v0.OldClientNetworkingHooks for mod fabric-networking-v0 (key client)
[16:08:49] [main/DEBUG] (FabricLoader/Entrypoint) Registering new-style initializer net.fabricmc.fabric.impl.networking.v0.OldNetworkingHooks for mod fabric-networking-v0 (key main)
[16:08:49] [main/DEBUG] (FabricLoader/Entrypoint) Registering new-style initializer net.fabricmc.fabric.impl.lookup.ApiLookupImpl for mod fabric-api-lookup-api-v1 (key main)
[16:08:49] [main/DEBUG] (FabricLoader/Entrypoint) Registering new-style initializer net.fabricmc.fabric.impl.screenhandler.client.ClientNetworking for mod fabric-screen-handler-api-v1 (key client)
[16:08:49] [main/DEBUG] (FabricLoader/Entrypoint) Registering new-style initializer net.fabricmc.fabric.impl.event.interaction.InteractionEventsRouterClient for mod fabric-events-interaction-v0 (key client)
[16:08:49] [main/DEBUG] (FabricLoader/Entrypoint) Registering new-style initializer net.fabricmc.fabric.impl.event.interaction.InteractionEventsRouter for mod fabric-events-interaction-v0 (key main)
[16:08:49] [main/DEBUG] (FabricLoader/Entrypoint) Registering new-style initializer net.fabricmc.fabric.impl.client.container.ScreenProviderRegistryImpl::init for mod fabric-containers-v0 (key client)
[16:08:49] [main/INFO] (FabricLoader/Mixin) SpongePowered MIXIN Subsystem Version=0.8.7 Source=file:/home/<USER>/.gradle/caches/modules-2/files-2.1/net.fabricmc/sponge-mixin/0.15.4+mixin.0.8.7/6a12aacc794f1078458433116e9ed42c1cc98096/sponge-mixin-0.15.4+mixin.0.8.7.jar Service=Knot/Fabric Env=CLIENT
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Initialising Mixin Platform Manager
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Adding mixin platform agents for container ContainerHandleURI(file:///home/<USER>/.gradle/caches/modules-2/files-2.1/net.fabricmc/fabric-loader/0.16.13/98c9beaf1e9b7290882be44120b8a3967e7b1f28/fabric-loader-0.16.13.jar)
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Instancing new MixinPlatformAgentDefault for ContainerHandleURI(file:///home/<USER>/.gradle/caches/modules-2/files-2.1/net.fabricmc/fabric-loader/0.16.13/98c9beaf1e9b7290882be44120b8a3967e7b1f28/fabric-loader-0.16.13.jar)
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) MixinPlatformAgentDefault accepted container ContainerHandleURI(file:///home/<USER>/.gradle/caches/modules-2/files-2.1/net.fabricmc/fabric-loader/0.16.13/98c9beaf1e9b7290882be44120b8a3967e7b1f28/fabric-loader-0.16.13.jar)
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Instancing new MixinPlatformAgentDefault for ContainerHandleURI(file:///home/<USER>/.gradle/caches/modules-2/files-2.1/net.fabricmc/fabric-loader/0.16.13/98c9beaf1e9b7290882be44120b8a3967e7b1f28/fabric-loader-0.16.13.jar)
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) MixinPlatformAgentDefault accepted container ContainerHandleURI(file:///home/<USER>/.gradle/caches/modules-2/files-2.1/net.fabricmc/fabric-loader/0.16.13/98c9beaf1e9b7290882be44120b8a3967e7b1f28/fabric-loader-0.16.13.jar)
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Processing prepare() for PlatformAgent[MixinPlatformAgentDefault:ContainerHandleURI(file:///home/<USER>/.gradle/caches/modules-2/files-2.1/net.fabricmc/fabric-loader/0.16.13/98c9beaf1e9b7290882be44120b8a3967e7b1f28/fabric-loader-0.16.13.jar)]
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Processing prepare() for PlatformAgent[MixinPlatformAgentDefault:ContainerHandleURI(file:///home/<USER>/.gradle/caches/modules-2/files-2.1/net.fabricmc/fabric-loader/0.16.13/98c9beaf1e9b7290882be44120b8a3967e7b1f28/fabric-loader-0.16.13.jar)]
[16:08:49] [main/INFO] (FabricLoader/Mixin) Loaded Fabric development mappings for mixin remapper!
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Compatibility level JAVA_16 specified by fabric-dimensions-v1.mixins.json is higher than the maximum level supported by this version of mixin (JAVA_13).
[16:08:49] [main/INFO] (FabricLoader/Mixin) Compatibility level set to JAVA_16
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-dimensions-v1.mixins.json does not specify "minVersion" or "requiredFeatures" property
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Compatibility level JAVA_17 specified by fabric-item-group-api-v1.mixins.json is higher than the maximum level supported by this version of mixin (JAVA_13).
[16:08:49] [main/INFO] (FabricLoader/Mixin) Compatibility level set to JAVA_17
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-item-group-api-v1.mixins.json does not specify "minVersion" or "requiredFeatures" property
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-item-group-api-v1.client.mixins.json does not specify "minVersion" or "requiredFeatures" property
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Compatibility level JAVA_16 specified by fabric-object-builder-v1.mixins.json is higher than the maximum level supported by this version of mixin (JAVA_13).
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-object-builder-v1.mixins.json does not specify "minVersion" or "requiredFeatures" property
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Compatibility level JAVA_16 specified by fabric-object-builder-v1.client.mixins.json is higher than the maximum level supported by this version of mixin (JAVA_13).
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-object-builder-v1.client.mixins.json does not specify "minVersion" or "requiredFeatures" property
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Mixin config pokecobbleclaim.mixins.json does not specify "minVersion" or "requiredFeatures" property
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-block-view-api-v2.mixins.json does not specify "minVersion" or "requiredFeatures" property
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-block-view-api-v2.client.mixins.json does not specify "minVersion" or "requiredFeatures" property
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Compatibility level JAVA_16 specified by fabric-lifecycle-events-v1.mixins.json is higher than the maximum level supported by this version of mixin (JAVA_13).
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-lifecycle-events-v1.mixins.json does not specify "minVersion" or "requiredFeatures" property
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Compatibility level JAVA_16 specified by fabric-lifecycle-events-v1.client.mixins.json is higher than the maximum level supported by this version of mixin (JAVA_13).
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-lifecycle-events-v1.client.mixins.json does not specify "minVersion" or "requiredFeatures" property
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Compatibility level JAVA_16 specified by fabric-resource-conditions-api-v1.mixins.json is higher than the maximum level supported by this version of mixin (JAVA_13).
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-resource-conditions-api-v1.mixins.json does not specify "minVersion" or "requiredFeatures" property
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Compatibility level JAVA_16 specified by fabric-screen-api-v1.mixins.json is higher than the maximum level supported by this version of mixin (JAVA_13).
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-screen-api-v1.mixins.json does not specify "minVersion" or "requiredFeatures" property
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Compatibility level JAVA_16 specified by fabric-gametest-api-v1.mixins.json is higher than the maximum level supported by this version of mixin (JAVA_13).
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-gametest-api-v1.mixins.json does not specify "minVersion" or "requiredFeatures" property
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-rendering-data-attachment-v1.mixins.json does not specify "minVersion" or "requiredFeatures" property
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-rendering-data-attachment-v1.client.mixins.json does not specify "minVersion" or "requiredFeatures" property
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-sound-api-v1.mixins.json does not specify "minVersion" or "requiredFeatures" property
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Compatibility level JAVA_16 specified by fabric-message-api-v1.mixins.json is higher than the maximum level supported by this version of mixin (JAVA_13).
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-message-api-v1.mixins.json does not specify "minVersion" or "requiredFeatures" property
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-message-api-v1.client.mixins.json does not specify "minVersion" or "requiredFeatures" property
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Compatibility level JAVA_16 specified by fabric-renderer-api-v1.mixins.json is higher than the maximum level supported by this version of mixin (JAVA_13).
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-renderer-api-v1.mixins.json does not specify "minVersion" or "requiredFeatures" property
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Compatibility level JAVA_16 specified by fabric-renderer-api-v1.debughud.mixins.json is higher than the maximum level supported by this version of mixin (JAVA_13).
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-renderer-api-v1.debughud.mixins.json does not specify "minVersion" or "requiredFeatures" property
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Compatibility level JAVA_16 specified by fabric-entity-events-v1.mixins.json is higher than the maximum level supported by this version of mixin (JAVA_13).
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-entity-events-v1.mixins.json does not specify "minVersion" or "requiredFeatures" property
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Compatibility level JAVA_16 specified by fabric-entity-events-v1.client.mixins.json is higher than the maximum level supported by this version of mixin (JAVA_13).
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-entity-events-v1.client.mixins.json does not specify "minVersion" or "requiredFeatures" property
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Compatibility level JAVA_16 specified by fabric-game-rule-api-v1.mixins.json is higher than the maximum level supported by this version of mixin (JAVA_13).
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-game-rule-api-v1.mixins.json does not specify "minVersion" or "requiredFeatures" property
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Compatibility level JAVA_16 specified by fabric-game-rule-api-v1.client.mixins.json is higher than the maximum level supported by this version of mixin (JAVA_13).
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-game-rule-api-v1.client.mixins.json does not specify "minVersion" or "requiredFeatures" property
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Compatibility level JAVA_16 specified by fabric-registry-sync-v0.mixins.json is higher than the maximum level supported by this version of mixin (JAVA_13).
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-registry-sync-v0.mixins.json does not specify "minVersion" or "requiredFeatures" property
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Compatibility level JAVA_16 specified by fabric-registry-sync-v0.client.mixins.json is higher than the maximum level supported by this version of mixin (JAVA_13).
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-registry-sync-v0.client.mixins.json does not specify "minVersion" or "requiredFeatures" property
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-block-api-v1.mixins.json does not specify "minVersion" or "requiredFeatures" property
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Compatibility level JAVA_16 specified by fabric-biome-api-v1.mixins.json is higher than the maximum level supported by this version of mixin (JAVA_13).
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-biome-api-v1.mixins.json does not specify "minVersion" or "requiredFeatures" property
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Compatibility level JAVA_16 specified by fabric-renderer-indigo.mixins.json is higher than the maximum level supported by this version of mixin (JAVA_13).
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-renderer-indigo.mixins.json does not specify "minVersion" or "requiredFeatures" property
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Compatibility level JAVA_16 specified by fabric-resource-loader-v0.mixins.json is higher than the maximum level supported by this version of mixin (JAVA_13).
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-resource-loader-v0.mixins.json does not specify "minVersion" or "requiredFeatures" property
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Compatibility level JAVA_16 specified by fabric-resource-loader-v0.client.mixins.json is higher than the maximum level supported by this version of mixin (JAVA_13).
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-resource-loader-v0.client.mixins.json does not specify "minVersion" or "requiredFeatures" property
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Compatibility level JAVA_16 specified by fabric-key-binding-api-v1.mixins.json is higher than the maximum level supported by this version of mixin (JAVA_13).
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-key-binding-api-v1.mixins.json does not specify "minVersion" or "requiredFeatures" property
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-recipe-api-v1.mixins.json does not specify "minVersion" or "requiredFeatures" property
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Compatibility level JAVA_16 specified by fabric-transfer-api-v1.mixins.json is higher than the maximum level supported by this version of mixin (JAVA_13).
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-transfer-api-v1.mixins.json does not specify "minVersion" or "requiredFeatures" property
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Compatibility level JAVA_16 specified by fabric-content-registries-v0.mixins.json is higher than the maximum level supported by this version of mixin (JAVA_13).
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-content-registries-v0.mixins.json does not specify "minVersion" or "requiredFeatures" property
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Compatibility level JAVA_16 specified by fabric-loot-tables-v1.mixins.json is higher than the maximum level supported by this version of mixin (JAVA_13).
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-loot-tables-v1.mixins.json does not specify "minVersion" or "requiredFeatures" property
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-model-loading-api-v1.mixins.json does not specify "minVersion" or "requiredFeatures" property
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Compatibility level JAVA_16 specified by fabric-networking-api-v1.mixins.json is higher than the maximum level supported by this version of mixin (JAVA_13).
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-networking-api-v1.mixins.json does not specify "minVersion" or "requiredFeatures" property
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Compatibility level JAVA_16 specified by fabric-networking-api-v1.client.mixins.json is higher than the maximum level supported by this version of mixin (JAVA_13).
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-networking-api-v1.client.mixins.json does not specify "minVersion" or "requiredFeatures" property
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-data-attachment-api-v1.mixins.json does not specify "minVersion" or "requiredFeatures" property
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-data-attachment-api-v1.client.mixins.json does not specify "minVersion" or "requiredFeatures" property
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Compatibility level JAVA_16 specified by fabric-mining-level-api-v1.mixins.json is higher than the maximum level supported by this version of mixin (JAVA_13).
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-mining-level-api-v1.mixins.json does not specify "minVersion" or "requiredFeatures" property
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Compatibility level JAVA_16 specified by fabric-command-api-v2.mixins.json is higher than the maximum level supported by this version of mixin (JAVA_13).
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-command-api-v2.mixins.json does not specify "minVersion" or "requiredFeatures" property
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Compatibility level JAVA_16 specified by fabric-command-api-v2.client.mixins.json is higher than the maximum level supported by this version of mixin (JAVA_13).
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-command-api-v2.client.mixins.json does not specify "minVersion" or "requiredFeatures" property
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Compatibility level JAVA_16 specified by fabric-crash-report-info-v1.mixins.json is higher than the maximum level supported by this version of mixin (JAVA_13).
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-crash-report-info-v1.mixins.json does not specify "minVersion" or "requiredFeatures" property
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Compatibility level JAVA_16 specified by fabric-api-lookup-api-v1.mixins.json is higher than the maximum level supported by this version of mixin (JAVA_13).
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-api-lookup-api-v1.mixins.json does not specify "minVersion" or "requiredFeatures" property
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Compatibility level JAVA_16 specified by fabric-item-api-v1.mixins.json is higher than the maximum level supported by this version of mixin (JAVA_13).
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-item-api-v1.mixins.json does not specify "minVersion" or "requiredFeatures" property
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Compatibility level JAVA_16 specified by fabric-item-api-v1.client.mixins.json is higher than the maximum level supported by this version of mixin (JAVA_13).
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-item-api-v1.client.mixins.json does not specify "minVersion" or "requiredFeatures" property
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Compatibility level JAVA_16 specified by fabric-blockrenderlayer-v1.mixins.json is higher than the maximum level supported by this version of mixin (JAVA_13).
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-blockrenderlayer-v1.mixins.json does not specify "minVersion" or "requiredFeatures" property
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-loot-api-v2.mixins.json does not specify "minVersion" or "requiredFeatures" property
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Compatibility level JAVA_16 specified by fabric-screen-handler-api-v1.mixins.json is higher than the maximum level supported by this version of mixin (JAVA_13).
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-screen-handler-api-v1.mixins.json does not specify "minVersion" or "requiredFeatures" property
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Compatibility level JAVA_16 specified by fabric-data-generation-api-v1.mixins.json is higher than the maximum level supported by this version of mixin (JAVA_13).
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-data-generation-api-v1.mixins.json does not specify "minVersion" or "requiredFeatures" property
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Compatibility level JAVA_16 specified by fabric-data-generation-api-v1.client.mixins.json is higher than the maximum level supported by this version of mixin (JAVA_13).
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-data-generation-api-v1.client.mixins.json does not specify "minVersion" or "requiredFeatures" property
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Compatibility level JAVA_16 specified by fabric-particles-v1.client.mixins.json is higher than the maximum level supported by this version of mixin (JAVA_13).
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-particles-v1.client.mixins.json does not specify "minVersion" or "requiredFeatures" property
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Compatibility level JAVA_16 specified by fabric-events-interaction-v0.mixins.json is higher than the maximum level supported by this version of mixin (JAVA_13).
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-events-interaction-v0.mixins.json does not specify "minVersion" or "requiredFeatures" property
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Compatibility level JAVA_16 specified by fabric-events-interaction-v0.client.mixins.json is higher than the maximum level supported by this version of mixin (JAVA_13).
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-events-interaction-v0.client.mixins.json does not specify "minVersion" or "requiredFeatures" property
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Compatibility level JAVA_16 specified by fabric-rendering-v1.mixins.json is higher than the maximum level supported by this version of mixin (JAVA_13).
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-rendering-v1.mixins.json does not specify "minVersion" or "requiredFeatures" property
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Compatibility level JAVA_16 specified by fabric-rendering-fluids-v1.mixins.json is higher than the maximum level supported by this version of mixin (JAVA_13).
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-rendering-fluids-v1.mixins.json does not specify "minVersion" or "requiredFeatures" property
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Compatibility level JAVA_16 specified by fabric-containers-v0.mixins.json is higher than the maximum level supported by this version of mixin (JAVA_13).
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-containers-v0.mixins.json does not specify "minVersion" or "requiredFeatures" property
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Compatibility level JAVA_16 specified by fabric-containers-v0.accurate.mixins.json is higher than the maximum level supported by this version of mixin (JAVA_13).
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Mixin config fabric-containers-v0.accurate.mixins.json does not specify "minVersion" or "requiredFeatures" property
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/minecraftMaven/net/minecraft/minecraft-merged-7787b014d4/1.20.1-net.fabricmc.yarn.1_20_1.1.20.1+build.10-v2/minecraft-merged-7787b014d4-1.20.1-net.fabricmc.yarn.1_20_1.1.20.1+build.10-v2.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/build/classes/java/main to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/build/resources/main to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/.gradle/caches/fabric-loom/1.20.1/net.fabricmc.yarn.1_20_1.1.20.1+build.10-v2/mappings.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/.gradle/caches/modules-2/files-2.1/io.github.llamalad7/mixinextras-fabric/0.4.1/8d1a9e96afb990367fa1f904d17580d164da72e3/mixinextras-fabric-0.4.1.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/.gradle/caches/modules-2/files-2.1/net.fabricmc/dev-launch-injector/0.2.1+build.8/da8bef7e6e2f952da707f282bdb46882a0fce5e3/dev-launch-injector-0.2.1+build.8.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/.gradle/caches/modules-2/files-2.1/com.github.oshi/oshi-core/6.2.2/54f5efc19bca95d709d9a37d19ffcbba3d21c1a6/oshi-core-6.2.2.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/.gradle/caches/modules-2/files-2.1/com.google.guava/failureaccess/1.0.1/1dcf1de382a0bf95a3d8b0849546c88bac1292c9/failureaccess-1.0.1.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/.gradle/caches/modules-2/files-2.1/com.google.guava/guava/31.1-jre/60458f877d055d0c9114d9e1a2efb737b4bc282c/guava-31.1-jre.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/.gradle/caches/modules-2/files-2.1/com.ibm.icu/icu4j/71.1/9e7d3304c23f9ba5cb71915f7cce23231a57a445/icu4j-71.1.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/.gradle/caches/modules-2/files-2.1/com.mojang/blocklist/1.0.10/5c685c5ffa94c4cd39496c7184c1d122e515ecef/blocklist-1.0.10.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/.gradle/caches/modules-2/files-2.1/com.mojang/brigadier/1.1.8/5244ce82c3337bba4a196a3ce858bfaecc74404a/brigadier-1.1.8.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/.gradle/caches/modules-2/files-2.1/com.mojang/datafixerupper/6.0.8/3ba4a30557a9b057760af4011f909ba619fc5125/datafixerupper-6.0.8.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/.gradle/caches/modules-2/files-2.1/com.mojang/patchy/2.2.10/da05971b07cbb379d002cf7eaec6a2048211fefc/patchy-2.2.10.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/.gradle/caches/modules-2/files-2.1/com.mojang/text2speech/1.17.9/3cad216e3a7f0c19b4b394388bc9ffc446f13b14/text2speech-1.17.9.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/.gradle/caches/modules-2/files-2.1/commons-codec/commons-codec/1.15/49d94806b6e3dc933dacbd8acb0fdbab8ebd1e5d/commons-codec-1.15.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/.gradle/caches/modules-2/files-2.1/commons-io/commons-io/2.11.0/a2503f302b11ebde7ebc3df41daebe0e4eea3689/commons-io-2.11.0.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/.gradle/caches/modules-2/files-2.1/commons-logging/commons-logging/1.2/4bfc12adfe4842bf07b657f0369c4cb522955686/commons-logging-1.2.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/.gradle/caches/modules-2/files-2.1/io.netty/netty-buffer/4.1.82.Final/a544270cf1ae8b8077082f5036436a9a9971ea71/netty-buffer-4.1.82.Final.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/.gradle/caches/modules-2/files-2.1/io.netty/netty-codec/4.1.82.Final/b77200379acb345a9ffdece1c605e591ac3e4e0a/netty-codec-4.1.82.Final.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/.gradle/caches/modules-2/files-2.1/io.netty/netty-common/4.1.82.Final/22d148e85c3f5ebdacc0ce1f5aabb1d420f73f3/netty-common-4.1.82.Final.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/.gradle/caches/modules-2/files-2.1/io.netty/netty-handler/4.1.82.Final/644041d1fa96a5d3130a29e8978630d716d76e38/netty-handler-4.1.82.Final.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/.gradle/caches/modules-2/files-2.1/io.netty/netty-resolver/4.1.82.Final/38f665ae8dcd29032eea31245ba7806bed2e0fa8/netty-resolver-4.1.82.Final.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/.gradle/caches/modules-2/files-2.1/io.netty/netty-transport-classes-epoll/4.1.82.Final/e7c7dd18deac93105797f30057c912651ea76521/netty-transport-classes-epoll-4.1.82.Final.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/.gradle/caches/modules-2/files-2.1/io.netty/netty-transport-native-epoll/4.1.82.Final/476409d6255001ca53a55f65b01c13822f8dc93a/netty-transport-native-epoll-4.1.82.Final-linux-aarch_64.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/.gradle/caches/modules-2/files-2.1/io.netty/netty-transport-native-epoll/4.1.82.Final/c7350a71920f3ae9142945e25fed4846cce53374/netty-transport-native-epoll-4.1.82.Final-linux-x86_64.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/.gradle/caches/modules-2/files-2.1/io.netty/netty-transport-native-unix-common/4.1.82.Final/3e895b35ca1b8a0eca56cacff4c2dde5d2c6abce/netty-transport-native-unix-common-4.1.82.Final.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/.gradle/caches/modules-2/files-2.1/io.netty/netty-transport/4.1.82.Final/e431a218d91acb6476ccad5f5aafde50aa3945ca/netty-transport-4.1.82.Final.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/.gradle/caches/modules-2/files-2.1/it.unimi.dsi/fastutil/8.5.9/bb7ea75ecdb216654237830b3a96d87ad91f8cc5/fastutil-8.5.9.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/.gradle/caches/modules-2/files-2.1/net.java.dev.jna/jna-platform/5.12.1/97406a297c852f4a41e688a176ec675f72e8329/jna-platform-5.12.1.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/.gradle/caches/modules-2/files-2.1/net.java.dev.jna/jna/5.12.1/b1e93a735caea94f503e95e6fe79bf9cdc1e985d/jna-5.12.1.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/.gradle/caches/modules-2/files-2.1/net.sf.jopt-simple/jopt-simple/5.0.4/4fdac2fbe92dfad86aa6e9301736f6b4342a3f5c/jopt-simple-5.0.4.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/.gradle/caches/modules-2/files-2.1/org.apache.commons/commons-compress/1.21/4ec95b60d4e86b5c95a0e919cb172a0af98011ef/commons-compress-1.21.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/.gradle/caches/modules-2/files-2.1/org.apache.commons/commons-lang3/3.12.0/c6842c86792ff03b9f1d1fe2aab8dc23aa6c6f0e/commons-lang3-3.12.0.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/.gradle/caches/modules-2/files-2.1/org.apache.httpcomponents/httpclient/4.5.13/e5f6cae5ca7ecaac1ec2827a9e2d65ae2869cada/httpclient-4.5.13.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/.gradle/caches/modules-2/files-2.1/org.apache.httpcomponents/httpcore/4.4.15/7f2e0c573eaa7a74bac2e89b359e1f73d92a0a1d/httpcore-4.4.15.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/.gradle/caches/modules-2/files-2.1/org.joml/joml/1.10.5/22566d58af70ad3d72308bab63b8339906deb649/joml-1.10.5.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/.gradle/caches/modules-2/files-2.1/org.lwjgl/lwjgl-glfw/3.3.1/cbac1b8d30cb4795149c1ef540f912671a8616d0/lwjgl-glfw-3.3.1.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/.gradle/caches/modules-2/files-2.1/org.lwjgl/lwjgl-glfw/3.3.1/81716978214ecbda15050ca394b06ef61501a49e/lwjgl-glfw-3.3.1-natives-linux.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/.gradle/caches/modules-2/files-2.1/org.lwjgl/lwjgl-jemalloc/3.3.1/a817bcf213db49f710603677457567c37d53e103/lwjgl-jemalloc-3.3.1.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/.gradle/caches/modules-2/files-2.1/org.lwjgl/lwjgl-jemalloc/3.3.1/33dbb017b6ed6b25f993ad9d56741a49e7937718/lwjgl-jemalloc-3.3.1-natives-linux.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/.gradle/caches/modules-2/files-2.1/org.lwjgl/lwjgl-openal/3.3.1/2623a6b8ae1dfcd880738656a9f0243d2e6840bd/lwjgl-openal-3.3.1.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/.gradle/caches/modules-2/files-2.1/org.lwjgl/lwjgl-openal/3.3.1/f906b6439f6daa66001182ea7727e3467a93681b/lwjgl-openal-3.3.1-natives-linux.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/.gradle/caches/modules-2/files-2.1/org.lwjgl/lwjgl-opengl/3.3.1/831a5533a21a5f4f81bbc51bb13e9899319b5411/lwjgl-opengl-3.3.1.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/.gradle/caches/modules-2/files-2.1/org.lwjgl/lwjgl-opengl/3.3.1/ab9ab6fde3743e3550fa5d46d785ecb45b047d99/lwjgl-opengl-3.3.1-natives-linux.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/.gradle/caches/modules-2/files-2.1/org.lwjgl/lwjgl-stb/3.3.1/b119297cf8ed01f247abe8685857f8e7fcf5980f/lwjgl-stb-3.3.1.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/.gradle/caches/modules-2/files-2.1/org.lwjgl/lwjgl-stb/3.3.1/3ee7aec8686e52867677110415566a5342a80230/lwjgl-stb-3.3.1-natives-linux.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/.gradle/caches/modules-2/files-2.1/org.lwjgl/lwjgl-tinyfd/3.3.1/ff1914111ef2e3e0110ef2dabc8d8cdaad82347/lwjgl-tinyfd-3.3.1.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/.gradle/caches/modules-2/files-2.1/org.lwjgl/lwjgl-tinyfd/3.3.1/a35110b9471bea8cde826ab297550ee8c22f5035/lwjgl-tinyfd-3.3.1-natives-linux.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/.gradle/caches/modules-2/files-2.1/org.lwjgl/lwjgl/3.3.1/ae58664f88e18a9bb2c77b063833ca7aaec484cb/lwjgl-3.3.1.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/.gradle/caches/modules-2/files-2.1/org.lwjgl/lwjgl/3.3.1/1de885aba434f934201b99f2f1afb142036ac189/lwjgl-3.3.1-natives-linux.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-api/0.92.5+1.20.1/fabric-api-0.92.5+1.20.1.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-transfer-api-v1/3.3.6+8dd72ea377/fabric-transfer-api-v1-3.3.6+8dd72ea377.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-api-lookup-api-v1/1.6.37+1802ada577/fabric-api-lookup-api-v1-1.6.37+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-blockrenderlayer-v1/1.1.42+1802ada577/fabric-blockrenderlayer-v1-1.1.42+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-client-tags-api-v1/1.1.3+1802ada577/fabric-client-tags-api-v1-1.1.3+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-command-api-v1/1.2.35+f71b366f77/fabric-command-api-v1-1.2.35+f71b366f77.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-commands-v0/0.2.52+df3654b377/fabric-commands-v0-0.2.52+df3654b377.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-command-api-v2/2.2.14+1802ada577/fabric-command-api-v2-2.2.14+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-content-registries-v0/4.0.13+1802ada577/fabric-content-registries-v0-4.0.13+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-convention-tags-v1/1.5.6+1802ada577/fabric-convention-tags-v1-1.5.6+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-data-attachment-api-v1/1.0.2+de0fd6d177/fabric-data-attachment-api-v1-1.0.2+de0fd6d177.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-data-generation-api-v1/12.3.6+1802ada577/fabric-data-generation-api-v1-12.3.6+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-dimensions-v1/2.1.55+1802ada577/fabric-dimensions-v1-2.1.55+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-entity-events-v1/1.6.1+1c78457f77/fabric-entity-events-v1-1.6.1+1c78457f77.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-events-interaction-v0/0.6.4+13a40c6677/fabric-events-interaction-v0-0.6.4+13a40c6677.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-gametest-api-v1/1.2.15+1802ada577/fabric-gametest-api-v1-1.2.15+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-events-lifecycle-v0/0.2.64+df3654b377/fabric-events-lifecycle-v0-0.2.64+df3654b377.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-item-api-v1/2.1.29+1802ada577/fabric-item-api-v1-2.1.29+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-item-group-api-v1/4.0.14+1802ada577/fabric-item-group-api-v1-4.0.14+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-mining-level-api-v1/2.1.52+1802ada577/fabric-mining-level-api-v1-2.1.52+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-lifecycle-events-v1/2.2.23+1802ada577/fabric-lifecycle-events-v1-2.2.23+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-loot-tables-v1/1.1.47+9e7660c677/fabric-loot-tables-v1-1.1.47+9e7660c677.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-loot-api-v2/1.2.3+1802ada577/fabric-loot-api-v2-1.2.3+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-message-api-v1/5.1.10+1802ada577/fabric-message-api-v1-5.1.10+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-models-v0/0.4.3+9386d8a777/fabric-models-v0-0.4.3+9386d8a777.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-model-loading-api-v1/1.0.4+1802ada577/fabric-model-loading-api-v1-1.0.4+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-recipe-api-v1/1.0.23+1802ada577/fabric-recipe-api-v1-1.0.23+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-registry-sync-v0/2.3.5+1802ada577/fabric-registry-sync-v0-2.3.5+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-screen-handler-api-v1/1.3.32+1802ada577/fabric-screen-handler-api-v1-1.3.32+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-containers-v0/0.1.66+df3654b377/fabric-containers-v0-0.1.66+df3654b377.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-networking-v0/0.3.53+df3654b377/fabric-networking-v0-0.3.53+df3654b377.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-networking-api-v1/1.3.13+13a40c6677/fabric-networking-api-v1-1.3.13+13a40c6677.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-object-builder-api-v1/11.1.5+e35120df77/fabric-object-builder-api-v1-11.1.5+e35120df77.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-particles-v1/1.1.3+1802ada577/fabric-particles-v1-1.1.3+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-renderer-indigo/1.5.3+85287f9f77/fabric-renderer-indigo-1.5.3+85287f9f77.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-renderer-api-v1/3.2.2+1802ada577/fabric-renderer-api-v1-3.2.2+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-rendering-fluids-v1/3.0.29+1802ada577/fabric-rendering-fluids-v1-3.0.29+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-renderer-registries-v1/3.2.47+df3654b377/fabric-renderer-registries-v1-3.2.47+df3654b377.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-rendering-v0/1.1.50+df3654b377/fabric-rendering-v0-1.1.50+df3654b377.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-rendering-v1/3.0.9+1802ada577/fabric-rendering-v1-3.0.9+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-screen-api-v1/2.0.9+1802ada577/fabric-screen-api-v1-2.0.9+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-api-base/0.4.32+1802ada577/fabric-api-base-0.4.32+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-biome-api-v1/13.0.14+1802ada577/fabric-biome-api-v1-13.0.14+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-block-api-v1/1.0.12+1802ada577/fabric-block-api-v1-1.0.12+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-rendering-data-attachment-v1/0.3.39+92a0d36777/fabric-rendering-data-attachment-v1-0.3.39+92a0d36777.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-block-view-api-v2/1.0.3+924f046a77/fabric-block-view-api-v2-1.0.3+924f046a77.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-crash-report-info-v1/0.2.20+1802ada577/fabric-crash-report-info-v1-0.2.20+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-game-rule-api-v1/1.0.41+1802ada577/fabric-game-rule-api-v1-1.0.41+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-keybindings-v0/0.2.36+df3654b377/fabric-keybindings-v0-0.2.36+df3654b377.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-key-binding-api-v1/1.0.38+1802ada577/fabric-key-binding-api-v1-1.0.38+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-resource-conditions-api-v1/2.3.9+1802ada577/fabric-resource-conditions-api-v1-2.3.9+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-resource-loader-v0/0.11.12+fb82e9d777/fabric-resource-loader-v0-0.11.12+fb82e9d777.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-sound-api-v1/1.0.14+1802ada577/fabric-sound-api-v1-1.0.14+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/.gradle/loom-cache/remapped_mods/net_fabricmc_yarn_1_20_1_1_20_1_build_10_v2/net/fabricmc/fabric-api/fabric-transitive-access-wideners-v1/4.3.2+1802ada577/fabric-transitive-access-wideners-v1-4.3.2+1802ada577.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/.gradle/caches/modules-2/files-2.1/org.jline/jline-reader/3.20.0/8f15415b022a25b473e8e16c28ae913186ffb9c4/jline-reader-3.20.0.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Knot) Adding /home/<USER>/.gradle/caches/modules-2/files-2.1/org.jline/jline-terminal/3.20.0/d0ddcc708ddf527a3454c941b7b9225cc83a15ff/jline-terminal-3.20.0.jar to classpath.
[16:08:49] [main/DEBUG] (FabricLoader/Entrypoint) No subscribers for entrypoint 'preLaunch'
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Preparing mixins for MixinEnvironment[DEFAULT]
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-dimensions-v1.mixins.json
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Remapping refMap fabric-dimensions-v1-refmap.json using remapper chain
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-item-group-api-v1.mixins.json
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Remapping refMap fabric-item-group-api-v1-refmap.json using remapper chain
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-item-group-api-v1.client.mixins.json
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Remapping refMap client-fabric-item-group-api-v1-refmap.json using remapper chain
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-object-builder-v1.mixins.json
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Remapping refMap fabric-object-builder-api-v1-refmap.json using remapper chain
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-object-builder-v1.client.mixins.json
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Remapping refMap client-fabric-object-builder-api-v1-refmap.json using remapper chain
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Selecting config pokecobbleclaim.mixins.json
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-block-view-api-v2.mixins.json
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Remapping refMap fabric-block-view-api-v2-refmap.json using remapper chain
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-block-view-api-v2.client.mixins.json
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Remapping refMap client-fabric-block-view-api-v2-refmap.json using remapper chain
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-lifecycle-events-v1.mixins.json
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Remapping refMap fabric-lifecycle-events-v1-refmap.json using remapper chain
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-lifecycle-events-v1.client.mixins.json
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Remapping refMap client-fabric-lifecycle-events-v1-refmap.json using remapper chain
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-resource-conditions-api-v1.mixins.json
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Remapping refMap fabric-resource-conditions-api-v1-refmap.json using remapper chain
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-screen-api-v1.mixins.json
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Remapping refMap client-fabric-screen-api-v1-refmap.json using remapper chain
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-gametest-api-v1.mixins.json
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Remapping refMap fabric-gametest-api-v1-refmap.json using remapper chain
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-rendering-data-attachment-v1.mixins.json
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Remapping refMap fabric-rendering-data-attachment-v1-refmap.json using remapper chain
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-rendering-data-attachment-v1.client.mixins.json
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Remapping refMap client-fabric-rendering-data-attachment-v1-refmap.json using remapper chain
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-sound-api-v1.mixins.json
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Remapping refMap client-fabric-sound-api-v1-refmap.json using remapper chain
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-message-api-v1.mixins.json
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Remapping refMap fabric-message-api-v1-refmap.json using remapper chain
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-message-api-v1.client.mixins.json
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Remapping refMap client-fabric-message-api-v1-refmap.json using remapper chain
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-renderer-api-v1.mixins.json
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Remapping refMap client-fabric-renderer-api-v1-refmap.json using remapper chain
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-renderer-api-v1.debughud.mixins.json
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Remapping refMap client-fabric-renderer-api-v1-refmap.json using remapper chain
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-entity-events-v1.mixins.json
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Remapping refMap fabric-entity-events-v1-refmap.json using remapper chain
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-entity-events-v1.client.mixins.json
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Remapping refMap client-fabric-entity-events-v1-refmap.json using remapper chain
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-game-rule-api-v1.mixins.json
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Remapping refMap fabric-game-rule-api-v1-refmap.json using remapper chain
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-game-rule-api-v1.client.mixins.json
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Remapping refMap client-fabric-game-rule-api-v1-refmap.json using remapper chain
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-registry-sync-v0.mixins.json
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Remapping refMap fabric-registry-sync-v0-refmap.json using remapper chain
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-registry-sync-v0.client.mixins.json
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Remapping refMap client-fabric-registry-sync-v0-refmap.json using remapper chain
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-block-api-v1.mixins.json
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Remapping refMap fabric-block-api-v1-refmap.json using remapper chain
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-biome-api-v1.mixins.json
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Remapping refMap fabric-biome-api-v1-refmap.json using remapper chain
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-renderer-indigo.mixins.json
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Remapping refMap client-fabric-renderer-indigo-refmap.json using remapper chain
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-resource-loader-v0.mixins.json
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Remapping refMap fabric-resource-loader-v0-refmap.json using remapper chain
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-resource-loader-v0.client.mixins.json
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Remapping refMap client-fabric-resource-loader-v0-refmap.json using remapper chain
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-key-binding-api-v1.mixins.json
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Remapping refMap client-fabric-key-binding-api-v1-refmap.json using remapper chain
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-recipe-api-v1.mixins.json
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Remapping refMap fabric-recipe-api-v1-refmap.json using remapper chain
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-transfer-api-v1.mixins.json
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Remapping refMap fabric-transfer-api-v1-refmap.json using remapper chain
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-content-registries-v0.mixins.json
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Remapping refMap fabric-content-registries-v0-refmap.json using remapper chain
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-loot-tables-v1.mixins.json
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Remapping refMap fabric-loot-tables-v1-refmap.json using remapper chain
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-model-loading-api-v1.mixins.json
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Remapping refMap client-fabric-model-loading-api-v1-refmap.json using remapper chain
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-networking-api-v1.mixins.json
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Remapping refMap fabric-networking-api-v1-refmap.json using remapper chain
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-networking-api-v1.client.mixins.json
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Remapping refMap client-fabric-networking-api-v1-refmap.json using remapper chain
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-data-attachment-api-v1.mixins.json
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Remapping refMap fabric-data-attachment-api-v1-refmap.json using remapper chain
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-data-attachment-api-v1.client.mixins.json
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Remapping refMap client-fabric-data-attachment-api-v1-refmap.json using remapper chain
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-mining-level-api-v1.mixins.json
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Remapping refMap fabric-mining-level-api-v1-refmap.json using remapper chain
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-command-api-v2.mixins.json
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Remapping refMap fabric-command-api-v2-refmap.json using remapper chain
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-command-api-v2.client.mixins.json
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Remapping refMap client-fabric-command-api-v2-refmap.json using remapper chain
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-crash-report-info-v1.mixins.json
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Remapping refMap fabric-crash-report-info-v1-refmap.json using remapper chain
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-api-lookup-api-v1.mixins.json
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Remapping refMap fabric-api-lookup-api-v1-refmap.json using remapper chain
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-item-api-v1.mixins.json
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Remapping refMap fabric-item-api-v1-refmap.json using remapper chain
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-item-api-v1.client.mixins.json
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Remapping refMap client-fabric-item-api-v1-refmap.json using remapper chain
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-blockrenderlayer-v1.mixins.json
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Remapping refMap client-fabric-blockrenderlayer-v1-refmap.json using remapper chain
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-loot-api-v2.mixins.json
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Remapping refMap fabric-loot-api-v2-refmap.json using remapper chain
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-screen-handler-api-v1.mixins.json
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Remapping refMap fabric-screen-handler-api-v1-refmap.json using remapper chain
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-data-generation-api-v1.mixins.json
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Remapping refMap fabric-data-generation-api-v1-refmap.json using remapper chain
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-data-generation-api-v1.client.mixins.json
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Remapping refMap client-fabric-data-generation-api-v1-refmap.json using remapper chain
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-particles-v1.client.mixins.json
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Remapping refMap client-fabric-particles-v1-refmap.json using remapper chain
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-events-interaction-v0.mixins.json
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Remapping refMap fabric-events-interaction-v0-refmap.json using remapper chain
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-events-interaction-v0.client.mixins.json
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Remapping refMap client-fabric-events-interaction-v0-refmap.json using remapper chain
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Selecting config mixinextras.init.mixins.json
[16:08:49] [main/DEBUG] (FabricLoader/MixinExtras|Service) com.llamalad7.mixinextras.service.MixinExtrasServiceImpl(version=0.4.1) is taking over from null
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Registering new injector for @Inject with org.spongepowered.asm.mixin.injection.struct.CallbackInjectionInfo
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Registering new injector for @ModifyArg with org.spongepowered.asm.mixin.injection.struct.ModifyArgInjectionInfo
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Registering new injector for @ModifyArgs with org.spongepowered.asm.mixin.injection.struct.ModifyArgsInjectionInfo
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Registering new injector for @Redirect with org.spongepowered.asm.mixin.injection.struct.RedirectInjectionInfo
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Registering new injector for @ModifyVariable with org.spongepowered.asm.mixin.injection.struct.ModifyVariableInjectionInfo
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Registering new injector for @ModifyConstant with org.spongepowered.asm.mixin.injection.struct.ModifyConstantInjectionInfo
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-rendering-v1.mixins.json
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Remapping refMap client-fabric-rendering-v1-refmap.json using remapper chain
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-rendering-fluids-v1.mixins.json
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Remapping refMap client-fabric-rendering-fluids-v1-refmap.json using remapper chain
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-containers-v0.mixins.json
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Remapping refMap fabric-containers-v0-refmap.json using remapper chain
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Selecting config fabric-containers-v0.accurate.mixins.json
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Remapping refMap fabric-containers-v0-refmap.json using remapper chain
[16:08:49] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-dimensions-v1.mixins.json (5)
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-item-group-api-v1.mixins.json (3)
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-item-group-api-v1.client.mixins.json (1)
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-object-builder-v1.mixins.json (7)
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-object-builder-v1.client.mixins.json (5)
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) Preparing pokecobbleclaim.mixins.json (7)
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-block-view-api-v2.mixins.json (3)
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-block-view-api-v2.client.mixins.json (2)
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-lifecycle-events-v1.mixins.json (9)
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) @Mixin target net/minecraft/server/world/ServerWorld$ServerEntityHandler is public in fabric-lifecycle-events-v1.mixins.json:ServerWorldServerEntityHandlerMixin from mod fabric-lifecycle-events-v1 and should be specified in value
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-lifecycle-events-v1.client.mixins.json (6)
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) @Mixin target net/minecraft/client/world/ClientWorld$ClientEntityHandler is public in fabric-lifecycle-events-v1.client.mixins.json:ClientWorldClientEntityHandlerMixin from mod fabric-lifecycle-events-v1 and should be specified in value
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-resource-conditions-api-v1.mixins.json (6)
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-screen-api-v1.mixins.json (6)
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-gametest-api-v1.mixins.json (7)
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-rendering-data-attachment-v1.mixins.json (2)
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-rendering-data-attachment-v1.client.mixins.json (1)
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-sound-api-v1.mixins.json (2)
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-message-api-v1.mixins.json (2)
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-message-api-v1.client.mixins.json (2)
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-renderer-api-v1.mixins.json (5)
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-renderer-api-v1.debughud.mixins.json (1)
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-entity-events-v1.mixins.json (11)
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-entity-events-v1.client.mixins.json (1)
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-game-rule-api-v1.mixins.json (6)
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) @Mixin target net/minecraft/server/command/GameRuleCommand$1 is public in fabric-game-rule-api-v1.mixins.json:GameRuleCommandVisitorMixin from mod fabric-game-rule-api-v1 and should be specified in value
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-game-rule-api-v1.client.mixins.json (3)
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) @Mixin target net/minecraft/client/gui/screen/world/EditGameRulesScreen$RuleListWidget$1 is public in fabric-game-rule-api-v1.client.mixins.json:RuleListWidgetVisitorMixin from mod fabric-game-rule-api-v1 and should be specified in value
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-registry-sync-v0.mixins.json (14)
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-registry-sync-v0.client.mixins.json (5)
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-block-api-v1.mixins.json (2)
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-biome-api-v1.mixins.json (9)
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) @Mixin target net/minecraft/world/biome/source/MultiNoiseBiomeSourceParameterList$Preset$1 is public in fabric-biome-api-v1.mixins.json:NetherBiomePresetMixin from mod fabric-biome-api-v1 and should be specified in value
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-renderer-indigo.mixins.json (5)
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-resource-loader-v0.mixins.json (12)
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-resource-loader-v0.client.mixins.json (6)
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-key-binding-api-v1.mixins.json (2)
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-recipe-api-v1.mixins.json (3)
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-transfer-api-v1.mixins.json (12)
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-content-registries-v0.mixins.json (11)
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-loot-tables-v1.mixins.json (2)
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-model-loading-api-v1.mixins.json (3)
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) @Mixin target net/minecraft/client/render/model/ModelLoader$BakerImpl is public in fabric-model-loading-api-v1.mixins.json:ModelLoaderBakerImplMixin from mod fabric-model-loading-api-v1 and should be specified in value
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-networking-api-v1.mixins.json (11)
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) @Mixin target net/minecraft/server/world/ThreadedAnvilChunkStorage$EntityTracker is public in fabric-networking-api-v1.mixins.json:accessor.EntityTrackerAccessor from mod fabric-networking-api-v1 and should be specified in value
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-networking-api-v1.client.mixins.json (5)
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-data-attachment-api-v1.mixins.json (8)
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-data-attachment-api-v1.client.mixins.json (1)
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-mining-level-api-v1.mixins.json (3)
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-command-api-v2.mixins.json (5)
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-command-api-v2.client.mixins.json (2)
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-crash-report-info-v1.mixins.json (1)
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-api-lookup-api-v1.mixins.json (2)
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-item-api-v1.mixins.json (7)
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-item-api-v1.client.mixins.json (3)
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-blockrenderlayer-v1.mixins.json (1)
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-loot-api-v2.mixins.json (5)
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-screen-handler-api-v1.mixins.json (2)
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-data-generation-api-v1.mixins.json (6)
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-data-generation-api-v1.client.mixins.json (1)
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-particles-v1.client.mixins.json (4)
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-events-interaction-v0.mixins.json (5)
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) @Mixin target net/minecraft/server/network/ServerPlayNetworkHandler$1 is public in fabric-events-interaction-v0.mixins.json:ServerPlayNetworkHandlerMixin from mod fabric-events-interaction-v0 and should be specified in value
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-events-interaction-v0.client.mixins.json (3)
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) Preparing mixinextras.init.mixins.json (0)
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-rendering-v1.mixins.json (17)
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) @Mixin target net/minecraft/client/gl/ShaderProgram$1 is public in fabric-rendering-v1.mixins.json:shader.ShaderProgramImportProcessorMixin from mod fabric-rendering-v1 and should be specified in value
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-rendering-fluids-v1.mixins.json (1)
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-containers-v0.mixins.json (1)
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) Preparing fabric-containers-v0.accurate.mixins.json (1)
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) Inner class net/fabricmc/fabric/mixin/itemgroup/ItemGroupsMixin$1ItemGroupPosition in net/fabricmc/fabric/mixin/itemgroup/ItemGroupsMixin on net/minecraft/item/ItemGroups gets unique name net/minecraft/item/ItemGroups$1ItemGroupPosition$529e5bdc5efb4878a1d594708678fad9
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) Inner class net/fabricmc/fabric/mixin/transfer/ChiseledBookshelfBlockEntityMixin$1 in net/fabricmc/fabric/mixin/transfer/ChiseledBookshelfBlockEntityMixin on net/minecraft/block/entity/ChiseledBookshelfBlockEntity gets unique name net/minecraft/block/entity/ChiseledBookshelfBlockEntity$Anonymous$499fa590095648b582cba226f057bf5f
[16:08:50] [main/DEBUG] (FabricLoader/Mixin) Prepared 280 mixins in 0.667 sec (2.4ms avg) (0ms load, 0ms transform, 0ms plugin)
[16:08:50] [main/DEBUG] (io.netty.util.internal.logging.InternalLoggerFactory) Using SLF4J as the default logging framework
[16:08:50] [main/DEBUG] (io.netty.util.ResourceLeakDetector) -Dio.netty.leakDetection.level: simple
[16:08:50] [main/DEBUG] (io.netty.util.ResourceLeakDetector) -Dio.netty.leakDetection.targetRecords: 4
[16:08:51] [main/INFO] (FabricLoader/MixinExtras|Service) Initializing MixinExtras via com.llamalad7.mixinextras.service.MixinExtrasServiceImpl(version=0.4.1).
[16:08:51] [main/DEBUG] (FabricLoader/Mixin) Registering new injector for @SugarWrapper with com.llamalad7.mixinextras.sugar.impl.SugarWrapperInjectionInfo
[16:08:51] [main/DEBUG] (FabricLoader/Mixin) Registering new injector for @FactoryRedirectWrapper with com.llamalad7.mixinextras.wrapper.factory.FactoryRedirectWrapperInjectionInfo
[16:08:51] [main/DEBUG] (FabricLoader/Mixin) Mixing SystemDetailsMixin from fabric-crash-report-info-v1.mixins.json into net.minecraft.util.SystemDetails
[16:08:51] [main/DEBUG] (FabricLoader/Mixin) fabric-crash-report-info-v1.mixins.json:SystemDetailsMixin from mod fabric-crash-report-info-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:51] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$appendMods$1(Lnet/fabricmc/loader/api/ModContainer;)Ljava/lang/String; to md5da3e1$fabric-crash-report-info-v1$lambda$appendMods$1$0 in fabric-crash-report-info-v1.mixins.json:SystemDetailsMixin from mod fabric-crash-report-info-v1
[16:08:51] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$fillSystemDetails$0()Ljava/lang/String; to md5da3e1$fabric-crash-report-info-v1$lambda$fillSystemDetails$0$1 in fabric-crash-report-info-v1.mixins.json:SystemDetailsMixin from mod fabric-crash-report-info-v1
[16:08:51] [main/DEBUG] (FabricLoader/Mixin) fabric-crash-report-info-v1.mixins.json:SystemDetailsMixin from mod fabric-crash-report-info-v1->@Inject::fillSystemDetails(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:51] [main/DEBUG] (FabricLoader/Mixin) fabric-crash-report-info-v1.mixins.json:SystemDetailsMixin from mod fabric-crash-report-info-v1->@Inject::fillSystemDetails(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:51] [main/DEBUG] (FabricLoader/Mixin) fabric-crash-report-info-v1.mixins.json:SystemDetailsMixin from mod fabric-crash-report-info-v1->@Inject::fillSystemDetails(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /proc/self/auxv
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /proc/self/auxv
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /proc/stat
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu0/topology/core_id
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu0/topology/core_id
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu0/topology/physical_package_id
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu0/topology/physical_package_id
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu0/cpu_capacity
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu1/topology/core_id
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu1/topology/core_id
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu1/topology/physical_package_id
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu1/topology/physical_package_id
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu1/cpu_capacity
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu10/topology/core_id
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu10/topology/core_id
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu10/topology/physical_package_id
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu10/topology/physical_package_id
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu10/cpu_capacity
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu11/topology/core_id
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu11/topology/core_id
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu11/topology/physical_package_id
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu11/topology/physical_package_id
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu11/cpu_capacity
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu12/topology/core_id
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu12/topology/core_id
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu12/topology/physical_package_id
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu12/topology/physical_package_id
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu12/cpu_capacity
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu13/topology/core_id
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu13/topology/core_id
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu13/topology/physical_package_id
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu13/topology/physical_package_id
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu13/cpu_capacity
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu14/topology/core_id
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu14/topology/core_id
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu14/topology/physical_package_id
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu14/topology/physical_package_id
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu14/cpu_capacity
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu15/topology/core_id
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu15/topology/core_id
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu15/topology/physical_package_id
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu15/topology/physical_package_id
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu15/cpu_capacity
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu2/topology/core_id
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu2/topology/core_id
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu2/topology/physical_package_id
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu2/topology/physical_package_id
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu2/cpu_capacity
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu3/topology/core_id
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu3/topology/core_id
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu3/topology/physical_package_id
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu3/topology/physical_package_id
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu3/cpu_capacity
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu4/topology/core_id
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu4/topology/core_id
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu4/topology/physical_package_id
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu4/topology/physical_package_id
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu4/cpu_capacity
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu5/topology/core_id
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu5/topology/core_id
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu5/topology/physical_package_id
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu5/topology/physical_package_id
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu5/cpu_capacity
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu6/topology/core_id
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu6/topology/core_id
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu6/topology/physical_package_id
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu6/topology/physical_package_id
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu6/cpu_capacity
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu7/topology/core_id
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu7/topology/core_id
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu7/topology/physical_package_id
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu7/topology/physical_package_id
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu7/cpu_capacity
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu8/topology/core_id
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu8/topology/core_id
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu8/topology/physical_package_id
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu8/topology/physical_package_id
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu8/cpu_capacity
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu9/topology/core_id
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu9/topology/core_id
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu9/topology/physical_package_id
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu9/topology/physical_package_id
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu9/cpu_capacity
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /proc/cpuinfo
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /proc/self/auxv
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /proc/meminfo
[16:08:51] [main/DEBUG] (oshi.util.FileUtil) Reading file /proc/meminfo
[16:08:51] [main/DEBUG] (FabricLoader/Mixin) Mixing BootstrapMixin from fabric-registry-sync-v0.mixins.json into net.minecraft.Bootstrap
[16:08:51] [main/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.mixins.json:BootstrapMixin from mod fabric-registry-sync-v0: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:51] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$initialize$1(Lnet/minecraft/fluid/Fluid;)Ljava/util/Collection; to md5da3e1$fabric-registry-sync-v0$lambda$initialize$1$0 in fabric-registry-sync-v0.mixins.json:BootstrapMixin from mod fabric-registry-sync-v0
[16:08:51] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$initialize$0(Lnet/minecraft/block/Block;)Ljava/util/Collection; to md5da3e1$fabric-registry-sync-v0$lambda$initialize$0$1 in fabric-registry-sync-v0.mixins.json:BootstrapMixin from mod fabric-registry-sync-v0
[16:08:51] [main/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.mixins.json:BootstrapMixin from mod fabric-registry-sync-v0->@Inject::initialize(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:51] [main/DEBUG] (FabricLoader/Mixin) Mixing RegistriesAccessor from fabric-registry-sync-v0.mixins.json into net.minecraft.registry.Registries
[16:08:51] [main/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.mixins.json:RegistriesAccessor from mod fabric-registry-sync-v0: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:51] [main/DEBUG] (FabricLoader/Mixin) Renaming @Accessor method getROOT()Lnet/minecraft/registry/MutableRegistry; to getROOT$fabric-registry-sync-v0_$md$5da3e1$0 in fabric-registry-sync-v0.mixins.json:RegistriesAccessor from mod fabric-registry-sync-v0
[16:08:51] [main/DEBUG] (FabricLoader/Mixin) Mixing RegistriesMixin from fabric-registry-sync-v0.mixins.json into net.minecraft.registry.Registries
[16:08:51] [main/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.mixins.json:RegistriesMixin from mod fabric-registry-sync-v0: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:51] [main/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.mixins.json:RegistriesMixin from mod fabric-registry-sync-v0->@Inject::init(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[16:08:51] [main/DEBUG] (FabricLoader/Mixin) Mixing SimpleRegistryMixin from fabric-registry-sync-v0.mixins.json into net.minecraft.registry.SimpleRegistry
[16:08:51] [main/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.mixins.json:SimpleRegistryMixin from mod fabric-registry-sync-v0: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:51] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$new$5([Lnet/fabricmc/fabric/api/event/registry/RegistryIdRemapCallback;)Lnet/fabricmc/fabric/api/event/registry/RegistryIdRemapCallback; to md5da3e1$fabric-registry-sync-v0$lambda$new$5$0 in fabric-registry-sync-v0.mixins.json:SimpleRegistryMixin from mod fabric-registry-sync-v0
[16:08:51] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$new$4([Lnet/fabricmc/fabric/api/event/registry/RegistryIdRemapCallback;Lnet/fabricmc/fabric/api/event/registry/RegistryIdRemapCallback$RemapState;)V to md5da3e1$fabric-registry-sync-v0$lambda$new$4$1 in fabric-registry-sync-v0.mixins.json:SimpleRegistryMixin from mod fabric-registry-sync-v0
[16:08:51] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$new$3([Lnet/fabricmc/fabric/api/event/registry/RegistryEntryRemovedCallback;)Lnet/fabricmc/fabric/api/event/registry/RegistryEntryRemovedCallback; to md5da3e1$fabric-registry-sync-v0$lambda$new$3$2 in fabric-registry-sync-v0.mixins.json:SimpleRegistryMixin from mod fabric-registry-sync-v0
[16:08:51] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$new$2([Lnet/fabricmc/fabric/api/event/registry/RegistryEntryRemovedCallback;ILnet/minecraft/util/Identifier;Ljava/lang/Object;)V to md5da3e1$fabric-registry-sync-v0$lambda$new$2$3 in fabric-registry-sync-v0.mixins.json:SimpleRegistryMixin from mod fabric-registry-sync-v0
[16:08:51] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$new$1([Lnet/fabricmc/fabric/api/event/registry/RegistryEntryAddedCallback;)Lnet/fabricmc/fabric/api/event/registry/RegistryEntryAddedCallback; to md5da3e1$fabric-registry-sync-v0$lambda$new$1$4 in fabric-registry-sync-v0.mixins.json:SimpleRegistryMixin from mod fabric-registry-sync-v0
[16:08:51] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$new$0([Lnet/fabricmc/fabric/api/event/registry/RegistryEntryAddedCallback;ILnet/minecraft/util/Identifier;Ljava/lang/Object;)V to md5da3e1$fabric-registry-sync-v0$lambda$new$0$5 in fabric-registry-sync-v0.mixins.json:SimpleRegistryMixin from mod fabric-registry-sync-v0
[16:08:51] [main/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.mixins.json:SimpleRegistryMixin from mod fabric-registry-sync-v0->@Inject::add(Lnet/minecraft/registry/RegistryKey;Ljava/lang/Object;Lcom/mojang/serialization/Lifecycle;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V doesn't use it's CallbackInfoReturnable
[16:08:51] [main/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.mixins.json:SimpleRegistryMixin from mod fabric-registry-sync-v0->@Inject::add(Lnet/minecraft/registry/RegistryKey;Ljava/lang/Object;Lcom/mojang/serialization/Lifecycle;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V has 0 override(s) in child classes
[16:08:51] [main/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.mixins.json:SimpleRegistryMixin from mod fabric-registry-sync-v0->@Inject::add(Lnet/minecraft/registry/RegistryKey;Ljava/lang/Object;Lcom/mojang/serialization/Lifecycle;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V won't be passed a CallbackInfoReturnable as a result
[16:08:51] [main/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.mixins.json:SimpleRegistryMixin from mod fabric-registry-sync-v0->@Inject::set(ILnet/minecraft/registry/RegistryKey;Ljava/lang/Object;Lcom/mojang/serialization/Lifecycle;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[16:08:51] [main/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.mixins.json:SimpleRegistryMixin from mod fabric-registry-sync-v0->@Inject::setPre(ILnet/minecraft/registry/RegistryKey;Ljava/lang/Object;Lcom/mojang/serialization/Lifecycle;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V doesn't use it's CallbackInfoReturnable
[16:08:51] [main/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.mixins.json:SimpleRegistryMixin from mod fabric-registry-sync-v0->@Inject::setPre(ILnet/minecraft/registry/RegistryKey;Ljava/lang/Object;Lcom/mojang/serialization/Lifecycle;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V has 0 override(s) in child classes
[16:08:51] [main/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.mixins.json:SimpleRegistryMixin from mod fabric-registry-sync-v0->@Inject::setPre(ILnet/minecraft/registry/RegistryKey;Ljava/lang/Object;Lcom/mojang/serialization/Lifecycle;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V won't be passed a CallbackInfoReturnable as a result
[16:08:51] [main/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.mixins.json:SimpleRegistryMixin from mod fabric-registry-sync-v0->@Inject::setPost(ILnet/minecraft/registry/RegistryKey;Ljava/lang/Object;Lcom/mojang/serialization/Lifecycle;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V doesn't use it's CallbackInfoReturnable
[16:08:51] [main/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.mixins.json:SimpleRegistryMixin from mod fabric-registry-sync-v0->@Inject::setPost(ILnet/minecraft/registry/RegistryKey;Ljava/lang/Object;Lcom/mojang/serialization/Lifecycle;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V has 0 override(s) in child classes
[16:08:51] [main/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.mixins.json:SimpleRegistryMixin from mod fabric-registry-sync-v0->@Inject::setPost(ILnet/minecraft/registry/RegistryKey;Ljava/lang/Object;Lcom/mojang/serialization/Lifecycle;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V won't be passed a CallbackInfoReturnable as a result
[16:08:51] [main/DEBUG] (FabricLoader/Mixin) Mixing FluidMixin from fabric-transfer-api-v1.mixins.json into net.minecraft.fluid.Fluid
[16:08:51] [main/DEBUG] (FabricLoader/Mixin) fabric-transfer-api-v1.mixins.json:FluidMixin from mod fabric-transfer-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:51] [main/DEBUG] (FabricLoader/Mixin) fabric-transfer-api-v1.mixins.json:FluidMixin from mod fabric-transfer-api-v1->@Inject::hookGetBucketFillSound(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[16:08:51] [main/DEBUG] (FabricLoader/Mixin) Mixing BlockMixin from fabric-block-api-v1.mixins.json into net.minecraft.block.Block
[16:08:51] [main/DEBUG] (FabricLoader/Mixin) Mixing AbstractBlockAccessor from fabric-object-builder-v1.mixins.json into net.minecraft.block.AbstractBlock
[16:08:51] [main/DEBUG] (FabricLoader/Mixin) fabric-object-builder-v1.mixins.json:AbstractBlockAccessor from mod fabric-object-builder-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:51] [main/DEBUG] (FabricLoader/Mixin) Mixing ItemMixin from fabric-transfer-api-v1.mixins.json into net.minecraft.item.Item
[16:08:51] [main/DEBUG] (FabricLoader/Mixin) fabric-transfer-api-v1.mixins.json:ItemMixin from mod fabric-transfer-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:51] [main/DEBUG] (FabricLoader/Mixin) Mixing ItemMixin from fabric-item-api-v1.mixins.json into net.minecraft.item.Item
[16:08:51] [main/DEBUG] (FabricLoader/Mixin) fabric-item-api-v1.mixins.json:ItemMixin from mod fabric-item-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:51] [main/DEBUG] (FabricLoader/Mixin) fabric-item-api-v1.mixins.json:ItemMixin from mod fabric-item-api-v1->@Inject::onConstruct(Lnet/minecraft/item/Item$Settings;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:51] [main/DEBUG] (FabricLoader/Mixin) fabric-item-api-v1.mixins.json:ItemMixin from mod fabric-item-api-v1->@Inject::onConstruct(Lnet/minecraft/item/Item$Settings;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:51] [main/DEBUG] (FabricLoader/Mixin) fabric-item-api-v1.mixins.json:ItemMixin from mod fabric-item-api-v1->@Inject::onConstruct(Lnet/minecraft/item/Item$Settings;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:51] [main/DEBUG] (FabricLoader/Mixin) Mixing BlockEntityTypeAccessor from fabric-api-lookup-api-v1.mixins.json into net.minecraft.block.entity.BlockEntityType
[16:08:51] [main/DEBUG] (FabricLoader/Mixin) fabric-api-lookup-api-v1.mixins.json:BlockEntityTypeAccessor from mod fabric-api-lookup-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:51] [main/DEBUG] (FabricLoader/Mixin) Mixing ArgumentTypesMixin from fabric-gametest-api-v1.mixins.json into net.minecraft.command.argument.ArgumentTypes
[16:08:51] [main/DEBUG] (FabricLoader/Mixin) fabric-gametest-api-v1.mixins.json:ArgumentTypesMixin from mod fabric-gametest-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:51] [main/DEBUG] (FabricLoader/Mixin) Mixing ArgumentTypesAccessor from fabric-command-api-v2.mixins.json into net.minecraft.command.argument.ArgumentTypes
[16:08:51] [main/DEBUG] (FabricLoader/Mixin) fabric-command-api-v2.mixins.json:ArgumentTypesAccessor from mod fabric-command-api-v2: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:51] [main/DEBUG] (FabricLoader/Mixin) Renaming @Accessor method fabric_getClassMap()Ljava/util/Map; to fabric_getClassMap$fabric-command-api-v2_$md$5da3e1$0 in fabric-command-api-v2.mixins.json:ArgumentTypesAccessor from mod fabric-command-api-v2
[16:08:51] [main/DEBUG] (FabricLoader/Mixin) fabric-gametest-api-v1.mixins.json:ArgumentTypesMixin from mod fabric-gametest-api-v1->@Inject::register(Lnet/minecraft/registry/Registry;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V doesn't use it's CallbackInfoReturnable
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) Mixing ItemGroupsMixin from fabric-item-group-api-v1.mixins.json into net.minecraft.item.ItemGroups
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-item-group-api-v1.mixins.json:ItemGroupsMixin from mod fabric-item-group-api-v1->@Inject::collect(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) Mixing ItemGroupAccessor from fabric-item-group-api-v1.mixins.json into net.minecraft.item.ItemGroup
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) Mixing ItemGroupMixin from fabric-item-group-api-v1.mixins.json into net.minecraft.item.ItemGroup
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$getStacks$0(Lnet/minecraft/item/ItemGroup;)Ljava/lang/IllegalStateException; to md5da3e1$fabric-item-group-api-v1$lambda$getStacks$0$0 in fabric-item-group-api-v1.mixins.json:ItemGroupMixin from mod fabric-item-group-api-v1
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-item-group-api-v1.mixins.json:ItemGroupMixin from mod fabric-item-group-api-v1->@Inject::getStacks(Lnet/minecraft/item/ItemGroup$DisplayContext;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-item-group-api-v1.mixins.json:ItemGroupMixin from mod fabric-item-group-api-v1->@Inject::getStacks(Lnet/minecraft/item/ItemGroup$DisplayContext;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-item-group-api-v1.mixins.json:ItemGroupMixin from mod fabric-item-group-api-v1->@Inject::getStacks(Lnet/minecraft/item/ItemGroup$DisplayContext;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) Mixing FireBlockMixin from fabric-content-registries-v0.mixins.json into net.minecraft.block.FireBlock
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-content-registries-v0.mixins.json:FireBlockMixin from mod fabric-content-registries-v0: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-content-registries-v0.mixins.json:FireBlockMixin from mod fabric-content-registries-v0->@Inject::afterConstruct(Lnet/minecraft/block/AbstractBlock$Settings;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-content-registries-v0.mixins.json:FireBlockMixin from mod fabric-content-registries-v0->@Inject::afterConstruct(Lnet/minecraft/block/AbstractBlock$Settings;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-content-registries-v0.mixins.json:FireBlockMixin from mod fabric-content-registries-v0->@Inject::afterConstruct(Lnet/minecraft/block/AbstractBlock$Settings;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-content-registries-v0.mixins.json:FireBlockMixin from mod fabric-content-registries-v0->@Inject::getFabricBurnChance(Lnet/minecraft/block/BlockState;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-content-registries-v0.mixins.json:FireBlockMixin from mod fabric-content-registries-v0->@Inject::getFabricSpreadChance(Lnet/minecraft/block/BlockState;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) Mixing BlockViewMixin from fabric-block-view-api-v2.mixins.json into net.minecraft.world.BlockView
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) Mixing WorldMixin from fabric-lifecycle-events-v1.mixins.json into net.minecraft.world.World
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:WorldMixin from mod fabric-lifecycle-events-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) Mixing AttachmentTargetsMixin from fabric-data-attachment-api-v1.mixins.json into net.minecraft.world.World
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) Mixing WorldViewMixin from fabric-block-view-api-v2.mixins.json into net.minecraft.world.WorldView
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) Mixing WorldViewMixin from fabric-rendering-data-attachment-v1.mixins.json into net.minecraft.world.WorldView
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) Mixing ServerWorldMixin from fabric-lifecycle-events-v1.mixins.json into net.minecraft.server.world.ServerWorld
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:ServerWorldMixin from mod fabric-lifecycle-events-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) Mixing ServerWorldMixin from fabric-data-attachment-api-v1.mixins.json into net.minecraft.server.world.ServerWorld
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$createAttachmentsPersistentState$1(Lnet/minecraft/server/world/ServerWorld;)Lnet/fabricmc/fabric/impl/attachment/AttachmentPersistentState; to md5da3e1$fabric-data-attachment-api-v1$lambda$createAttachmentsPersistentState$1$0 in fabric-data-attachment-api-v1.mixins.json:ServerWorldMixin from mod fabric-data-attachment-api-v1
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$createAttachmentsPersistentState$0(Lnet/minecraft/server/world/ServerWorld;Lnet/minecraft/nbt/NbtCompound;)Lnet/fabricmc/fabric/impl/attachment/AttachmentPersistentState; to md5da3e1$fabric-data-attachment-api-v1$lambda$createAttachmentsPersistentState$0$1 in fabric-data-attachment-api-v1.mixins.json:ServerWorldMixin from mod fabric-data-attachment-api-v1
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) Mixing ServerWorldMixin from fabric-api-lookup-api-v1.mixins.json into net.minecraft.server.world.ServerWorld
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-api-lookup-api-v1.mixins.json:ServerWorldMixin from mod fabric-api-lookup-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$fabric_invalidateCache$5(Ljava/util/Map$Entry;)Z to md5da3e1$fabric-api-lookup-api-v1$lambda$fabric_invalidateCache$5$2 in fabric-api-lookup-api-v1.mixins.json:ServerWorldMixin from mod fabric-api-lookup-api-v1
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$fabric_invalidateCache$4(Ljava/lang/ref/WeakReference;)Z to md5da3e1$fabric-api-lookup-api-v1$lambda$fabric_invalidateCache$4$3 in fabric-api-lookup-api-v1.mixins.json:ServerWorldMixin from mod fabric-api-lookup-api-v1
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$fabric_invalidateCache$3(Ljava/lang/ref/WeakReference;)V to md5da3e1$fabric-api-lookup-api-v1$lambda$fabric_invalidateCache$3$4 in fabric-api-lookup-api-v1.mixins.json:ServerWorldMixin from mod fabric-api-lookup-api-v1
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$fabric_invalidateCache$2(Ljava/lang/ref/WeakReference;)Z to md5da3e1$fabric-api-lookup-api-v1$lambda$fabric_invalidateCache$2$5 in fabric-api-lookup-api-v1.mixins.json:ServerWorldMixin from mod fabric-api-lookup-api-v1
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$fabric_registerCache$1(Ljava/lang/ref/WeakReference;)Z to md5da3e1$fabric-api-lookup-api-v1$lambda$fabric_registerCache$1$6 in fabric-api-lookup-api-v1.mixins.json:ServerWorldMixin from mod fabric-api-lookup-api-v1
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$fabric_registerCache$0(Lnet/minecraft/util/math/BlockPos;)Ljava/util/List; to md5da3e1$fabric-api-lookup-api-v1$lambda$fabric_registerCache$0$7 in fabric-api-lookup-api-v1.mixins.json:ServerWorldMixin from mod fabric-api-lookup-api-v1
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:ServerWorldMixin from mod fabric-lifecycle-events-v1->@Inject::startWorldTick(Ljava/util/function/BooleanSupplier;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:ServerWorldMixin from mod fabric-lifecycle-events-v1->@Inject::startWorldTick(Ljava/util/function/BooleanSupplier;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:ServerWorldMixin from mod fabric-lifecycle-events-v1->@Inject::startWorldTick(Ljava/util/function/BooleanSupplier;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:ServerWorldMixin from mod fabric-lifecycle-events-v1->@Inject::endWorldTick(Ljava/util/function/BooleanSupplier;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:ServerWorldMixin from mod fabric-lifecycle-events-v1->@Inject::endWorldTick(Ljava/util/function/BooleanSupplier;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:ServerWorldMixin from mod fabric-lifecycle-events-v1->@Inject::endWorldTick(Ljava/util/function/BooleanSupplier;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-data-attachment-api-v1.mixins.json:ServerWorldMixin from mod fabric-data-attachment-api-v1->@Inject::createAttachmentsPersistentState(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-data-attachment-api-v1.mixins.json:ServerWorldMixin from mod fabric-data-attachment-api-v1->@Inject::createAttachmentsPersistentState(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-data-attachment-api-v1.mixins.json:ServerWorldMixin from mod fabric-data-attachment-api-v1->@Inject::createAttachmentsPersistentState(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) Mixing EntityMixin from fabric-dimensions-v1.mixins.json into net.minecraft.entity.Entity
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-dimensions-v1.mixins.json:EntityMixin from mod fabric-dimensions-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) Mixing EntityMixin from fabric-entity-events-v1.mixins.json into net.minecraft.entity.Entity
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:EntityMixin from mod fabric-entity-events-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) Mixing AttachmentTargetsMixin from fabric-data-attachment-api-v1.mixins.json into net.minecraft.entity.Entity
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) Mixing EntityMixin from fabric-data-attachment-api-v1.mixins.json into net.minecraft.entity.Entity
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-dimensions-v1.mixins.json:EntityMixin from mod fabric-dimensions-v1->@Inject::getTeleportTarget(Lnet/minecraft/server/world/ServerWorld;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:EntityMixin from mod fabric-entity-events-v1->@Inject::afterWorldChanged(Lnet/minecraft/server/world/ServerWorld;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:EntityMixin from mod fabric-entity-events-v1->@Inject::afterWorldChanged(Lnet/minecraft/server/world/ServerWorld;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:EntityMixin from mod fabric-entity-events-v1->@Inject::afterWorldChanged(Lnet/minecraft/server/world/ServerWorld;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:EntityMixin from mod fabric-entity-events-v1->@Inject::afterEntityTeleportedToWorld(Lnet/minecraft/server/world/ServerWorld;DDDLjava/util/Set;FFLorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;FLnet/minecraft/entity/Entity;)V doesn't use it's CallbackInfoReturnable
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:EntityMixin from mod fabric-entity-events-v1->@Inject::afterEntityTeleportedToWorld(Lnet/minecraft/server/world/ServerWorld;DDDLjava/util/Set;FFLorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;FLnet/minecraft/entity/Entity;)V has 0 override(s) in child classes
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:EntityMixin from mod fabric-entity-events-v1->@Inject::afterEntityTeleportedToWorld(Lnet/minecraft/server/world/ServerWorld;DDDLjava/util/Set;FFLorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;FLnet/minecraft/entity/Entity;)V won't be passed a CallbackInfoReturnable as a result
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-data-attachment-api-v1.mixins.json:EntityMixin from mod fabric-data-attachment-api-v1->@Inject::readEntityAttachments(Lnet/minecraft/nbt/NbtCompound;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-data-attachment-api-v1.mixins.json:EntityMixin from mod fabric-data-attachment-api-v1->@Inject::readEntityAttachments(Lnet/minecraft/nbt/NbtCompound;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-data-attachment-api-v1.mixins.json:EntityMixin from mod fabric-data-attachment-api-v1->@Inject::readEntityAttachments(Lnet/minecraft/nbt/NbtCompound;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-data-attachment-api-v1.mixins.json:EntityMixin from mod fabric-data-attachment-api-v1->@Inject::writeEntityAttachments(Lnet/minecraft/nbt/NbtCompound;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V doesn't use it's CallbackInfoReturnable
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-data-attachment-api-v1.mixins.json:EntityMixin from mod fabric-data-attachment-api-v1->@Inject::writeEntityAttachments(Lnet/minecraft/nbt/NbtCompound;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V has 0 override(s) in child classes
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-data-attachment-api-v1.mixins.json:EntityMixin from mod fabric-data-attachment-api-v1->@Inject::writeEntityAttachments(Lnet/minecraft/nbt/NbtCompound;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V won't be passed a CallbackInfoReturnable as a result
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) Mixing PlayerEntityMixin from fabric-entity-events-v1.mixins.json into net.minecraft.entity.player.PlayerEntity
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:PlayerEntityMixin from mod fabric-entity-events-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) Mixing elytra.PlayerEntityMixin from fabric-entity-events-v1.mixins.json into net.minecraft.entity.player.PlayerEntity
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:elytra.PlayerEntityMixin from mod fabric-entity-events-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:PlayerEntityMixin from mod fabric-entity-events-v1->@Inject::onTrySleep(Lnet/minecraft/util/math/BlockPos;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:PlayerEntityMixin from mod fabric-entity-events-v1->@Inject::onIsSleepingLongEnough(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:elytra.PlayerEntityMixin from mod fabric-entity-events-v1->@Inject::injectElytraCheck(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) Mixing LivingEntityMixin from fabric-lifecycle-events-v1.mixins.json into net.minecraft.entity.LivingEntity
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:LivingEntityMixin from mod fabric-lifecycle-events-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) Mixing LivingEntityMixin from fabric-entity-events-v1.mixins.json into net.minecraft.entity.LivingEntity
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:LivingEntityMixin from mod fabric-entity-events-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) Mixing elytra.LivingEntityMixin from fabric-entity-events-v1.mixins.json into net.minecraft.entity.LivingEntity
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:elytra.LivingEntityMixin from mod fabric-entity-events-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) Mixing LivingEntityMixin from fabric-item-api-v1.mixins.json into net.minecraft.entity.LivingEntity
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-item-api-v1.mixins.json:LivingEntityMixin from mod fabric-item-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:LivingEntityMixin from mod fabric-lifecycle-events-v1->@Inject::getEquipmentChanges(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;Ljava/util/Map;[Lnet/minecraft/entity/EquipmentSlot;IILnet/minecraft/entity/EquipmentSlot;Lnet/minecraft/item/ItemStack;Lnet/minecraft/item/ItemStack;)V doesn't use it's CallbackInfoReturnable
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:LivingEntityMixin from mod fabric-lifecycle-events-v1->@Inject::getEquipmentChanges(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;Ljava/util/Map;[Lnet/minecraft/entity/EquipmentSlot;IILnet/minecraft/entity/EquipmentSlot;Lnet/minecraft/item/ItemStack;Lnet/minecraft/item/ItemStack;)V has 0 override(s) in child classes
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:LivingEntityMixin from mod fabric-lifecycle-events-v1->@Inject::getEquipmentChanges(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;Ljava/util/Map;[Lnet/minecraft/entity/EquipmentSlot;IILnet/minecraft/entity/EquipmentSlot;Lnet/minecraft/item/ItemStack;Lnet/minecraft/item/ItemStack;)V won't be passed a CallbackInfoReturnable as a result
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:LivingEntityMixin from mod fabric-entity-events-v1->@Inject::notifyDeath(Lnet/minecraft/entity/damage/DamageSource;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:LivingEntityMixin from mod fabric-entity-events-v1->@Inject::notifyDeath(Lnet/minecraft/entity/damage/DamageSource;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:LivingEntityMixin from mod fabric-entity-events-v1->@Inject::notifyDeath(Lnet/minecraft/entity/damage/DamageSource;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:LivingEntityMixin from mod fabric-entity-events-v1->@Inject::beforeDamage(Lnet/minecraft/entity/damage/DamageSource;FLorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:LivingEntityMixin from mod fabric-entity-events-v1->@Inject::onSleep(Lnet/minecraft/util/math/BlockPos;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:LivingEntityMixin from mod fabric-entity-events-v1->@Inject::onSleep(Lnet/minecraft/util/math/BlockPos;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:LivingEntityMixin from mod fabric-entity-events-v1->@Inject::onSleep(Lnet/minecraft/util/math/BlockPos;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:LivingEntityMixin from mod fabric-entity-events-v1->@Inject::onWakeUp(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:LivingEntityMixin from mod fabric-entity-events-v1->@Inject::onWakeUp(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:LivingEntityMixin from mod fabric-entity-events-v1->@Inject::onWakeUp(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:LivingEntityMixin from mod fabric-entity-events-v1->@Inject::onIsSleepingInBed(Lnet/minecraft/util/math/BlockPos;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:elytra.LivingEntityMixin from mod fabric-entity-events-v1->@Inject::injectElytraTick(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-item-api-v1.mixins.json:LivingEntityMixin from mod fabric-item-api-v1->@Inject::onGetPreferredEquipmentSlot(Lnet/minecraft/item/ItemStack;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) Mixing IdListMixin from fabric-registry-sync-v0.mixins.json into net.minecraft.util.collection.IdList
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.mixins.json:IdListMixin from mod fabric-registry-sync-v0: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$fabric_remapIds$0(Lit/unimi/dsi/fastutil/ints/Int2IntMap;Ljava/lang/Object;Ljava/lang/Integer;)Ljava/lang/Integer; to md5da3e1$fabric-registry-sync-v0$lambda$fabric_remapIds$0$0 in fabric-registry-sync-v0.mixins.json:IdListMixin from mod fabric-registry-sync-v0
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) Mixing DetectorRailBlockMixin from fabric-object-builder-v1.mixins.json into net.minecraft.block.DetectorRailBlock
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-object-builder-v1.mixins.json:DetectorRailBlockMixin from mod fabric-object-builder-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$getCustomComparatorOutput$0(Lnet/minecraft/entity/Entity;)Z to md5da3e1$fabric-object-builder-api-v1$lambda$getCustomComparatorOutput$0$0 in fabric-object-builder-v1.mixins.json:DetectorRailBlockMixin from mod fabric-object-builder-api-v1
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-object-builder-v1.mixins.json:DetectorRailBlockMixin from mod fabric-object-builder-api-v1->@Inject::getCustomComparatorOutput(Lnet/minecraft/block/BlockState;Lnet/minecraft/world/World;Lnet/minecraft/util/math/BlockPos;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) Mixing DropperBlockMixin from fabric-transfer-api-v1.mixins.json into net.minecraft.block.DropperBlock
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-transfer-api-v1.mixins.json:DropperBlockMixin from mod fabric-transfer-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$hookDispense$0(Lnet/fabricmc/fabric/api/transfer/v1/item/ItemVariant;)Z to md5da3e1$fabric-transfer-api-v1$lambda$hookDispense$0$0 in fabric-transfer-api-v1.mixins.json:DropperBlockMixin from mod fabric-transfer-api-v1
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-transfer-api-v1.mixins.json:DropperBlockMixin from mod fabric-transfer-api-v1->@Inject::hookDispense(Lnet/minecraft/server/world/ServerWorld;Lnet/minecraft/util/math/BlockPos;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) Mixing BeehiveBlockMixin from fabric-events-interaction-v0.mixins.json into net.minecraft.block.BeehiveBlock
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-events-interaction-v0.mixins.json:BeehiveBlockMixin from mod fabric-events-interaction-v0: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-events-interaction-v0.mixins.json:BeehiveBlockMixin from mod fabric-events-interaction-v0->@Inject::afterNearbyBeesPlayers(Lnet/minecraft/world/World;Lnet/minecraft/util/math/BlockPos;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V does use it's CallbackInfo
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) Mixing OxidizableMixin from fabric-content-registries-v0.mixins.json into net.minecraft.block.Oxidizable
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-content-registries-v0.mixins.json:OxidizableMixin from mod fabric-content-registries-v0: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-content-registries-v0.mixins.json:OxidizableMixin from mod fabric-content-registries-v0->@Inject::createOxidationLevelIncreasesMap(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) Mixing AbstractBlockSettingsAccessor from fabric-object-builder-v1.mixins.json into net.minecraft.block.AbstractBlock$Settings
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) fabric-object-builder-v1.mixins.json:AbstractBlockSettingsAccessor from mod fabric-object-builder-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:52] [main/DEBUG] (FabricLoader/Mixin) Mixing BlockStateMixin from fabric-block-api-v1.mixins.json into net.minecraft.block.BlockState
[16:08:53] [main/DEBUG] (FabricLoader/Mixin) Mixing BlockEntityMixin from fabric-block-view-api-v2.mixins.json into net.minecraft.block.entity.BlockEntity
[16:08:53] [main/DEBUG] (FabricLoader/Mixin) Mixing BlockEntityMixin from fabric-rendering-data-attachment-v1.mixins.json into net.minecraft.block.entity.BlockEntity
[16:08:53] [main/DEBUG] (FabricLoader/Mixin) Mixing AttachmentTargetsMixin from fabric-data-attachment-api-v1.mixins.json into net.minecraft.block.entity.BlockEntity
[16:08:53] [main/DEBUG] (FabricLoader/Mixin) Mixing BlockEntityMixin from fabric-data-attachment-api-v1.mixins.json into net.minecraft.block.entity.BlockEntity
[16:08:53] [main/DEBUG] (FabricLoader/Mixin) fabric-data-attachment-api-v1.mixins.json:BlockEntityMixin from mod fabric-data-attachment-api-v1->@Inject::readBlockEntityAttachments(Lnet/minecraft/nbt/NbtCompound;Ljava/lang/String;Lnet/minecraft/block/entity/BlockEntity;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V doesn't use it's CallbackInfoReturnable
[16:08:53] [main/DEBUG] (FabricLoader/Mixin) fabric-data-attachment-api-v1.mixins.json:BlockEntityMixin from mod fabric-data-attachment-api-v1->@Inject::writeBlockEntityAttachments(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[16:08:53] [main/DEBUG] (FabricLoader/Mixin) Mixing NamedScreenHandlerFactoryMixin from fabric-screen-handler-api-v1.mixins.json into net.minecraft.screen.NamedScreenHandlerFactory
[16:08:53] [main/DEBUG] (FabricLoader/Mixin) fabric-screen-handler-api-v1.mixins.json:NamedScreenHandlerFactoryMixin from mod fabric-screen-handler-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:53] [main/DEBUG] (FabricLoader/Mixin) Mixing LootableContainerBlockEntityMixin from fabric-transfer-api-v1.mixins.json into net.minecraft.block.entity.LootableContainerBlockEntity
[16:08:53] [main/DEBUG] (FabricLoader/Mixin) fabric-transfer-api-v1.mixins.json:LootableContainerBlockEntityMixin from mod fabric-transfer-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:53] [main/DEBUG] (FabricLoader/Mixin) Mixing ChiseledBookshelfBlockEntityMixin from fabric-transfer-api-v1.mixins.json into net.minecraft.block.entity.ChiseledBookshelfBlockEntity
[16:08:53] [main/DEBUG] (FabricLoader/Mixin) fabric-transfer-api-v1.mixins.json:ChiseledBookshelfBlockEntityMixin from mod fabric-transfer-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:53] [main/DEBUG] (FabricLoader/Mixin) fabric-transfer-api-v1.mixins.json:ChiseledBookshelfBlockEntityMixin from mod fabric-transfer-api-v1->@Inject::setStackBypass(ILnet/minecraft/item/ItemStack;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[16:08:53] [main/DEBUG] (FabricLoader/Mixin) Mixing AbstractFurnaceBlockEntityMixin from fabric-transfer-api-v1.mixins.json into net.minecraft.block.entity.AbstractFurnaceBlockEntity
[16:08:53] [main/DEBUG] (FabricLoader/Mixin) fabric-transfer-api-v1.mixins.json:AbstractFurnaceBlockEntityMixin from mod fabric-transfer-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:53] [main/DEBUG] (FabricLoader/Mixin) Mixing AbstractFurnaceBlockEntityMixin from fabric-content-registries-v0.mixins.json into net.minecraft.block.entity.AbstractFurnaceBlockEntity
[16:08:53] [main/DEBUG] (FabricLoader/Mixin) fabric-content-registries-v0.mixins.json:AbstractFurnaceBlockEntityMixin from mod fabric-content-registries-v0: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:53] [main/DEBUG] (FabricLoader/Mixin) Mixing AbstractFurnaceBlockEntityMixin from fabric-item-api-v1.mixins.json into net.minecraft.block.entity.AbstractFurnaceBlockEntity
[16:08:53] [main/DEBUG] (FabricLoader/Mixin) fabric-item-api-v1.mixins.json:AbstractFurnaceBlockEntityMixin from mod fabric-item-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:53] [main/DEBUG] (FabricLoader/Mixin) fabric-transfer-api-v1.mixins.json:AbstractFurnaceBlockEntityMixin from mod fabric-transfer-api-v1->@Inject::setStackSuppressUpdate(ILnet/minecraft/item/ItemStack;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[16:08:53] [main/DEBUG] (FabricLoader/Mixin) fabric-content-registries-v0.mixins.json:AbstractFurnaceBlockEntityMixin from mod fabric-content-registries-v0->@Inject::fuelTimeMapHook(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[16:08:53] [main/DEBUG] (FabricLoader/Mixin) fabric-item-api-v1.mixins.json:AbstractFurnaceBlockEntityMixin from mod fabric-item-api-v1->@Inject::getStackRemainder(Lnet/minecraft/world/World;Lnet/minecraft/util/math/BlockPos;Lnet/minecraft/block/BlockState;Lnet/minecraft/block/entity/AbstractFurnaceBlockEntity;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;ZZLnet/minecraft/item/ItemStack;ZZLnet/minecraft/recipe/Recipe;I)V doesn't use it's CallbackInfo
[16:08:53] [main/DEBUG] (FabricLoader/Mixin) Mixing JukeboxBlockEntityMixin from fabric-transfer-api-v1.mixins.json into net.minecraft.block.entity.JukeboxBlockEntity
[16:08:53] [main/DEBUG] (FabricLoader/Mixin) fabric-transfer-api-v1.mixins.json:JukeboxBlockEntityMixin from mod fabric-transfer-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:53] [main/DEBUG] (FabricLoader/Mixin) fabric-transfer-api-v1.mixins.json:JukeboxBlockEntityMixin from mod fabric-transfer-api-v1->@Inject::setStackBypass(ILnet/minecraft/item/ItemStack;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[16:08:53] [main/DEBUG] (FabricLoader/Mixin) Mixing MobEntityMixin from fabric-entity-events-v1.mixins.json into net.minecraft.entity.mob.MobEntity
[16:08:53] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:MobEntityMixin from mod fabric-entity-events-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:53] [main/DEBUG] (FabricLoader/Mixin) Mixing BrewingStandBlockEntityMixin from fabric-item-api-v1.mixins.json into net.minecraft.block.entity.BrewingStandBlockEntity
[16:08:53] [main/DEBUG] (FabricLoader/Mixin) fabric-item-api-v1.mixins.json:BrewingStandBlockEntityMixin from mod fabric-item-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:53] [main/DEBUG] (FabricLoader/Mixin) fabric-item-api-v1.mixins.json:BrewingStandBlockEntityMixin from mod fabric-item-api-v1->@Inject::captureItemStack(Lnet/minecraft/world/World;Lnet/minecraft/util/math/BlockPos;Lnet/minecraft/util/collection/DefaultedList;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Lnet/minecraft/item/ItemStack;)V doesn't use it's CallbackInfo
[16:08:53] [main/DEBUG] (FabricLoader/Mixin) Mixing ItemStackMixin from fabric-item-api-v1.mixins.json into net.minecraft.item.ItemStack
[16:08:53] [main/DEBUG] (FabricLoader/Mixin) fabric-item-api-v1.mixins.json:ItemStackMixin from mod fabric-item-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:53] [main/DEBUG] (FabricLoader/Mixin) Mixing ItemStackMixin from fabric-item-api-v1.client.mixins.json into net.minecraft.item.ItemStack
[16:08:53] [main/DEBUG] (FabricLoader/Mixin) fabric-item-api-v1.client.mixins.json:ItemStackMixin from mod fabric-item-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:53] [main/DEBUG] (FabricLoader/Mixin) fabric-item-api-v1.mixins.json:ItemStackMixin from mod fabric-item-api-v1->@Inject::saveDamager(ILnet/minecraft/entity/LivingEntity;Ljava/util/function/Consumer;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:53] [main/DEBUG] (FabricLoader/Mixin) fabric-item-api-v1.mixins.json:ItemStackMixin from mod fabric-item-api-v1->@Inject::clearDamage(ILnet/minecraft/entity/LivingEntity;Ljava/util/function/Consumer;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:53] [main/DEBUG] (FabricLoader/Mixin) fabric-item-api-v1.mixins.json:ItemStackMixin from mod fabric-item-api-v1->@Inject::clearDamage(ILnet/minecraft/entity/LivingEntity;Ljava/util/function/Consumer;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:53] [main/DEBUG] (FabricLoader/Mixin) fabric-item-api-v1.mixins.json:ItemStackMixin from mod fabric-item-api-v1->@Inject::clearDamage(ILnet/minecraft/entity/LivingEntity;Ljava/util/function/Consumer;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:53] [main/DEBUG] (FabricLoader/Mixin) fabric-item-api-v1.client.mixins.json:ItemStackMixin from mod fabric-item-api-v1->@Inject::getTooltip(Lnet/minecraft/entity/player/PlayerEntity;Lnet/minecraft/client/item/TooltipContext;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[16:08:53] [main/DEBUG] (FabricLoader/Mixin) Mixing HopperBlockEntityMixin from fabric-transfer-api-v1.mixins.json into net.minecraft.block.entity.HopperBlockEntity
[16:08:53] [main/DEBUG] (FabricLoader/Mixin) fabric-transfer-api-v1.mixins.json:HopperBlockEntityMixin from mod fabric-transfer-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:53] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$hookExtract$1(Lnet/fabricmc/fabric/api/transfer/v1/item/ItemVariant;)Z to md5da3e1$fabric-transfer-api-v1$lambda$hookExtract$1$0 in fabric-transfer-api-v1.mixins.json:HopperBlockEntityMixin from mod fabric-transfer-api-v1
[16:08:53] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$hookInsert$0(Lnet/fabricmc/fabric/api/transfer/v1/item/ItemVariant;)Z to md5da3e1$fabric-transfer-api-v1$lambda$hookInsert$0$1 in fabric-transfer-api-v1.mixins.json:HopperBlockEntityMixin from mod fabric-transfer-api-v1
[16:08:53] [main/DEBUG] (FabricLoader/Mixin) fabric-transfer-api-v1.mixins.json:HopperBlockEntityMixin from mod fabric-transfer-api-v1->@Inject::hookInsert(Lnet/minecraft/world/World;Lnet/minecraft/util/math/BlockPos;Lnet/minecraft/block/BlockState;Lnet/minecraft/inventory/Inventory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;Lnet/minecraft/inventory/Inventory;)V does use it's CallbackInfoReturnable
[16:08:53] [main/DEBUG] (FabricLoader/Mixin) fabric-transfer-api-v1.mixins.json:HopperBlockEntityMixin from mod fabric-transfer-api-v1->@Inject::hookExtract(Lnet/minecraft/world/World;Lnet/minecraft/block/entity/Hopper;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;Lnet/minecraft/inventory/Inventory;)V does use it's CallbackInfoReturnable
[16:08:54] [main/DEBUG] (FabricLoader/Mixin) Mixing TadpoleEntityMixin from fabric-entity-events-v1.mixins.json into net.minecraft.entity.passive.TadpoleEntity
[16:08:54] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:TadpoleEntityMixin from mod fabric-entity-events-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:54] [main/DEBUG] (FabricLoader/Mixin) Mixing ArmorItemMixin from fabric-item-api-v1.mixins.json into net.minecraft.item.ArmorItem
[16:08:54] [main/DEBUG] (FabricLoader/Mixin) fabric-item-api-v1.mixins.json:ArmorItemMixin from mod fabric-item-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:54] [main/DEBUG] (FabricLoader/Mixin) Mixing SwordItemMixin from fabric-mining-level-api-v1.mixins.json into net.minecraft.item.SwordItem
[16:08:54] [main/DEBUG] (FabricLoader/Mixin) fabric-mining-level-api-v1.mixins.json:SwordItemMixin from mod fabric-mining-level-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:54] [main/DEBUG] (FabricLoader/Mixin) fabric-mining-level-api-v1.mixins.json:SwordItemMixin from mod fabric-mining-level-api-v1->@Inject::fabric$onIsSuitableFor(Lnet/minecraft/block/BlockState;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[16:08:54] [main/DEBUG] (FabricLoader/Mixin) fabric-mining-level-api-v1.mixins.json:SwordItemMixin from mod fabric-mining-level-api-v1->@Inject::fabric$onGetMiningSpeedMultiplier(Lnet/minecraft/item/ItemStack;Lnet/minecraft/block/BlockState;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[16:08:54] [main/DEBUG] (FabricLoader/Mixin) fabric-mining-level-api-v1.mixins.json:SwordItemMixin from mod fabric-mining-level-api-v1->@Inject::fabric$onGetMiningSpeedMultiplier(Lnet/minecraft/item/ItemStack;Lnet/minecraft/block/BlockState;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[16:08:54] [main/DEBUG] (FabricLoader/Mixin) Mixing ShovelItemAccessor from fabric-content-registries-v0.mixins.json into net.minecraft.item.ShovelItem
[16:08:54] [main/DEBUG] (FabricLoader/Mixin) fabric-content-registries-v0.mixins.json:ShovelItemAccessor from mod fabric-content-registries-v0: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:54] [main/DEBUG] (FabricLoader/Mixin) Renaming @Accessor method getPathStates()Ljava/util/Map; to getPathStates$fabric-content-registries-v0_$md$5da3e1$0 in fabric-content-registries-v0.mixins.json:ShovelItemAccessor from mod fabric-content-registries-v0
[16:08:54] [main/DEBUG] (FabricLoader/Mixin) Mixing MiningToolItemMixin from fabric-mining-level-api-v1.mixins.json into net.minecraft.item.MiningToolItem
[16:08:54] [main/DEBUG] (FabricLoader/Mixin) fabric-mining-level-api-v1.mixins.json:MiningToolItemMixin from mod fabric-mining-level-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:54] [main/DEBUG] (FabricLoader/Mixin) fabric-mining-level-api-v1.mixins.json:MiningToolItemMixin from mod fabric-mining-level-api-v1->@Inject::fabric$onIsSuitableFor(Lnet/minecraft/block/BlockState;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;I)V does use it's CallbackInfoReturnable
[16:08:54] [main/DEBUG] (FabricLoader/Mixin) Mixing AxeItemAccessor from fabric-content-registries-v0.mixins.json into net.minecraft.item.AxeItem
[16:08:54] [main/DEBUG] (FabricLoader/Mixin) fabric-content-registries-v0.mixins.json:AxeItemAccessor from mod fabric-content-registries-v0: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:54] [main/DEBUG] (FabricLoader/Mixin) Renaming @Accessor method getStrippedBlocks()Ljava/util/Map; to getStrippedBlocks$fabric-content-registries-v0_$md$5da3e1$0 in fabric-content-registries-v0.mixins.json:AxeItemAccessor from mod fabric-content-registries-v0
[16:08:54] [main/DEBUG] (FabricLoader/Mixin) Renaming @Accessor method setStrippedBlocks(Ljava/util/Map;)V to setStrippedBlocks$fabric-content-registries-v0_$md$5da3e1$1 in fabric-content-registries-v0.mixins.json:AxeItemAccessor from mod fabric-content-registries-v0
[16:08:54] [main/DEBUG] (FabricLoader/Mixin) Mixing HoeItemAccessor from fabric-content-registries-v0.mixins.json into net.minecraft.item.HoeItem
[16:08:54] [main/DEBUG] (FabricLoader/Mixin) fabric-content-registries-v0.mixins.json:HoeItemAccessor from mod fabric-content-registries-v0: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:54] [main/DEBUG] (FabricLoader/Mixin) Renaming @Accessor method getTillingActions()Ljava/util/Map; to getTillingActions$fabric-content-registries-v0_$md$5da3e1$0 in fabric-content-registries-v0.mixins.json:HoeItemAccessor from mod fabric-content-registries-v0
[16:08:54] [main/DEBUG] (FabricLoader/Mixin) Mixing BucketItemAccessor from fabric-transfer-api-v1.mixins.json into net.minecraft.item.BucketItem
[16:08:54] [main/DEBUG] (FabricLoader/Mixin) fabric-transfer-api-v1.mixins.json:BucketItemAccessor from mod fabric-transfer-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:54] [main/DEBUG] (FabricLoader/Mixin) Mixing BucketItemMixin from fabric-transfer-api-v1.mixins.json into net.minecraft.item.BucketItem
[16:08:54] [main/DEBUG] (FabricLoader/Mixin) fabric-transfer-api-v1.mixins.json:BucketItemMixin from mod fabric-transfer-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:54] [main/DEBUG] (FabricLoader/Mixin) Mixing ShearsItemMixin from fabric-mining-level-api-v1.mixins.json into net.minecraft.item.ShearsItem
[16:08:54] [main/DEBUG] (FabricLoader/Mixin) fabric-mining-level-api-v1.mixins.json:ShearsItemMixin from mod fabric-mining-level-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:54] [main/DEBUG] (FabricLoader/Mixin) fabric-mining-level-api-v1.mixins.json:ShearsItemMixin from mod fabric-mining-level-api-v1->@Inject::fabric$onIsSuitableFor(Lnet/minecraft/block/BlockState;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[16:08:54] [main/DEBUG] (FabricLoader/Mixin) fabric-mining-level-api-v1.mixins.json:ShearsItemMixin from mod fabric-mining-level-api-v1->@Inject::fabric$onGetMiningSpeedMultiplier(Lnet/minecraft/item/ItemStack;Lnet/minecraft/block/BlockState;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[16:08:54] [main/DEBUG] (FabricLoader/Mixin) fabric-mining-level-api-v1.mixins.json:ShearsItemMixin from mod fabric-mining-level-api-v1->@Inject::fabric$onGetMiningSpeedMultiplier(Lnet/minecraft/item/ItemStack;Lnet/minecraft/block/BlockState;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[16:08:54] [main/DEBUG] (FabricLoader/Mixin) fabric-mining-level-api-v1.mixins.json:ShearsItemMixin from mod fabric-mining-level-api-v1->@Inject::fabric$onGetMiningSpeedMultiplier(Lnet/minecraft/item/ItemStack;Lnet/minecraft/block/BlockState;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[16:08:54] [main/DEBUG] (FabricLoader/Mixin) fabric-mining-level-api-v1.mixins.json:ShearsItemMixin from mod fabric-mining-level-api-v1->@Inject::fabric$onGetMiningSpeedMultiplier(Lnet/minecraft/item/ItemStack;Lnet/minecraft/block/BlockState;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[16:08:54] [main/DEBUG] (FabricLoader/Mixin) Mixing HoneycombItemMixin from fabric-content-registries-v0.mixins.json into net.minecraft.item.HoneycombItem
[16:08:54] [main/DEBUG] (FabricLoader/Mixin) fabric-content-registries-v0.mixins.json:HoneycombItemMixin from mod fabric-content-registries-v0: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:54] [main/DEBUG] (FabricLoader/Mixin) fabric-content-registries-v0.mixins.json:HoneycombItemMixin from mod fabric-content-registries-v0->@Inject::createUnwaxedToWaxedMap(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[16:08:54] [main/DEBUG] (FabricLoader/Mixin) Mixing StructuresToConfiguredStructuresFixMixin from fabric-registry-sync-v0.mixins.json into net.minecraft.datafixer.fix.StructuresToConfiguredStructuresFix
[16:08:54] [main/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.mixins.json:StructuresToConfiguredStructuresFixMixin from mod fabric-registry-sync-v0: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:54] [main/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.mixins.json:StructuresToConfiguredStructuresFixMixin from mod fabric-registry-sync-v0->@Inject::method_41022(Lcom/mojang/datafixers/util/Pair;Lcom/mojang/serialization/Dynamic;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[16:08:54] [main/DEBUG] (FabricLoader/Mixin) Mixing TaggedChoiceTypeMixin from fabric-dimensions-v1.mixins.json into com.mojang.datafixers.types.templates.TaggedChoice$TaggedChoiceType
[16:08:54] [main/DEBUG] (FabricLoader/Mixin) fabric-dimensions-v1.mixins.json:TaggedChoiceTypeMixin from mod fabric-dimensions-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:54] [main/DEBUG] (FabricLoader/Mixin) fabric-dimensions-v1.mixins.json:TaggedChoiceTypeMixin from mod fabric-dimensions-v1->@Inject::onGetCodec(Ljava/lang/Object;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[16:08:54] [main/DEBUG] (FabricLoader/Mixin) Mixing TaggedChoiceMixin from fabric-dimensions-v1.mixins.json into com.mojang.datafixers.types.templates.TaggedChoice
[16:08:54] [main/DEBUG] (FabricLoader/Mixin) fabric-dimensions-v1.mixins.json:TaggedChoiceMixin from mod fabric-dimensions-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:54] [main/DEBUG] (FabricLoader/Mixin) fabric-dimensions-v1.mixins.json:TaggedChoiceMixin from mod fabric-dimensions-v1->@Inject::onApply(Lcom/mojang/datafixers/util/Pair;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) Mixing Schema2832Mixin from fabric-dimensions-v1.mixins.json into net.minecraft.datafixer.schema.Schema2832
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) fabric-dimensions-v1.mixins.json:Schema2832Mixin from mod fabric-dimensions-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:55] [Datafixer Bootstrap/INFO] (com.mojang.datafixers.DataFixerBuilder) 188 Datafixer optimizations took 134 milliseconds
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) Mixing PigEntityMixin from fabric-entity-events-v1.mixins.json into net.minecraft.entity.passive.PigEntity
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:PigEntityMixin from mod fabric-entity-events-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) Mixing EntityMixin from fabric-dimensions-v1.mixins.json into net.minecraft.server.network.ServerPlayerEntity
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) fabric-dimensions-v1.mixins.json:EntityMixin from mod fabric-dimensions-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) Mixing ServerPlayerEntityMixin from fabric-entity-events-v1.mixins.json into net.minecraft.server.network.ServerPlayerEntity
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:ServerPlayerEntityMixin from mod fabric-entity-events-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) Mixing ServerPlayerEntityMixin from fabric-screen-handler-api-v1.mixins.json into net.minecraft.server.network.ServerPlayerEntity
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) fabric-screen-handler-api-v1.mixins.json:ServerPlayerEntityMixin from mod fabric-screen-handler-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) Mixing ServerPlayerEntityMixin from fabric-events-interaction-v0.mixins.json into net.minecraft.server.network.ServerPlayerEntity
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) fabric-events-interaction-v0.mixins.json:ServerPlayerEntityMixin from mod fabric-events-interaction-v0: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) Mixing ServerPlayerEntityAccessor from fabric-containers-v0.mixins.json into net.minecraft.server.network.ServerPlayerEntity
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) fabric-containers-v0.mixins.json:ServerPlayerEntityAccessor from mod fabric-containers-v0: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) Mixing ServerPlayerEntityMixin from fabric-containers-v0.accurate.mixins.json into net.minecraft.server.network.ServerPlayerEntity
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) fabric-containers-v0.accurate.mixins.json:ServerPlayerEntityMixin from mod fabric-containers-v0: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) fabric-dimensions-v1.mixins.json:EntityMixin from mod fabric-dimensions-v1->@Inject::getTeleportTarget(Lnet/minecraft/server/world/ServerWorld;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:ServerPlayerEntityMixin from mod fabric-entity-events-v1->@Inject::callOnKillForPlayer(Lnet/minecraft/entity/damage/DamageSource;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:ServerPlayerEntityMixin from mod fabric-entity-events-v1->@Inject::callOnKillForPlayer(Lnet/minecraft/entity/damage/DamageSource;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:ServerPlayerEntityMixin from mod fabric-entity-events-v1->@Inject::callOnKillForPlayer(Lnet/minecraft/entity/damage/DamageSource;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:ServerPlayerEntityMixin from mod fabric-entity-events-v1->@Inject::notifyDeath(Lnet/minecraft/entity/damage/DamageSource;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:ServerPlayerEntityMixin from mod fabric-entity-events-v1->@Inject::notifyDeath(Lnet/minecraft/entity/damage/DamageSource;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:ServerPlayerEntityMixin from mod fabric-entity-events-v1->@Inject::notifyDeath(Lnet/minecraft/entity/damage/DamageSource;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:ServerPlayerEntityMixin from mod fabric-entity-events-v1->@Inject::afterWorldChanged(Lnet/minecraft/server/world/ServerWorld;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:ServerPlayerEntityMixin from mod fabric-entity-events-v1->@Inject::afterWorldChanged(Lnet/minecraft/server/world/ServerWorld;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:ServerPlayerEntityMixin from mod fabric-entity-events-v1->@Inject::afterWorldChanged(Lnet/minecraft/server/world/ServerWorld;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:ServerPlayerEntityMixin from mod fabric-entity-events-v1->@Inject::onCopyFrom(Lnet/minecraft/server/network/ServerPlayerEntity;ZLorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:ServerPlayerEntityMixin from mod fabric-entity-events-v1->@Inject::onCopyFrom(Lnet/minecraft/server/network/ServerPlayerEntity;ZLorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:ServerPlayerEntityMixin from mod fabric-entity-events-v1->@Inject::onCopyFrom(Lnet/minecraft/server/network/ServerPlayerEntity;ZLorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:ServerPlayerEntityMixin from mod fabric-entity-events-v1->@Inject::onTrySleepDirectionCheck(Lnet/minecraft/util/math/BlockPos;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;Lnet/minecraft/util/math/Direction;)V does use it's CallbackInfoReturnable
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) fabric-screen-handler-api-v1.mixins.json:ServerPlayerEntityMixin from mod fabric-screen-handler-api-v1->@Inject::fabric_storeOpenedScreenHandler(Lnet/minecraft/screen/NamedScreenHandlerFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;Lnet/minecraft/screen/ScreenHandler;)V doesn't use it's CallbackInfoReturnable
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) fabric-screen-handler-api-v1.mixins.json:ServerPlayerEntityMixin from mod fabric-screen-handler-api-v1->@Inject::fabric_storeOpenedScreenHandler(Lnet/minecraft/screen/NamedScreenHandlerFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;Lnet/minecraft/screen/ScreenHandler;)V has 0 override(s) in child classes
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) fabric-screen-handler-api-v1.mixins.json:ServerPlayerEntityMixin from mod fabric-screen-handler-api-v1->@Inject::fabric_storeOpenedScreenHandler(Lnet/minecraft/screen/NamedScreenHandlerFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;Lnet/minecraft/screen/ScreenHandler;)V won't be passed a CallbackInfoReturnable as a result
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) fabric-events-interaction-v0.mixins.json:ServerPlayerEntityMixin from mod fabric-events-interaction-v0->@Inject::onPlayerInteractEntity(Lnet/minecraft/entity/Entity;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) Mixing AttachmentTargetsMixin from fabric-data-attachment-api-v1.mixins.json into net.minecraft.world.chunk.Chunk
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) Mixing WorldChunkMixin from fabric-lifecycle-events-v1.client.mixins.json into net.minecraft.world.chunk.WorldChunk
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:WorldChunkMixin from mod fabric-lifecycle-events-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) Mixing WorldChunkMixin from fabric-data-attachment-api-v1.mixins.json into net.minecraft.world.chunk.WorldChunk
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:WorldChunkMixin from mod fabric-lifecycle-events-v1->@Inject::onLoadBlockEntity(Lnet/minecraft/block/entity/BlockEntity;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Lnet/minecraft/util/math/BlockPos;Lnet/minecraft/block/entity/BlockEntity;)V doesn't use it's CallbackInfo
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:WorldChunkMixin from mod fabric-lifecycle-events-v1->@Inject::onLoadBlockEntity(Lnet/minecraft/block/entity/BlockEntity;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Lnet/minecraft/util/math/BlockPos;Lnet/minecraft/block/entity/BlockEntity;)V has 0 override(s) in child classes
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:WorldChunkMixin from mod fabric-lifecycle-events-v1->@Inject::onLoadBlockEntity(Lnet/minecraft/block/entity/BlockEntity;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Lnet/minecraft/util/math/BlockPos;Lnet/minecraft/block/entity/BlockEntity;)V won't be passed a CallbackInfo as a result
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:WorldChunkMixin from mod fabric-lifecycle-events-v1->@Inject::onRemoveBlockEntity(Lnet/minecraft/block/entity/BlockEntity;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Lnet/minecraft/util/math/BlockPos;Lnet/minecraft/block/entity/BlockEntity;)V doesn't use it's CallbackInfo
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:WorldChunkMixin from mod fabric-lifecycle-events-v1->@Inject::onRemoveBlockEntity(Lnet/minecraft/block/entity/BlockEntity;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Lnet/minecraft/util/math/BlockPos;Lnet/minecraft/block/entity/BlockEntity;)V has 0 override(s) in child classes
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:WorldChunkMixin from mod fabric-lifecycle-events-v1->@Inject::onRemoveBlockEntity(Lnet/minecraft/block/entity/BlockEntity;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Lnet/minecraft/util/math/BlockPos;Lnet/minecraft/block/entity/BlockEntity;)V won't be passed a CallbackInfo as a result
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:WorldChunkMixin from mod fabric-lifecycle-events-v1->@Inject::onRemoveBlockEntity(Lnet/minecraft/util/math/BlockPos;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Lnet/minecraft/block/entity/BlockEntity;)V doesn't use it's CallbackInfo
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:WorldChunkMixin from mod fabric-lifecycle-events-v1->@Inject::onRemoveBlockEntity(Lnet/minecraft/util/math/BlockPos;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Lnet/minecraft/block/entity/BlockEntity;)V has 0 override(s) in child classes
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:WorldChunkMixin from mod fabric-lifecycle-events-v1->@Inject::onRemoveBlockEntity(Lnet/minecraft/util/math/BlockPos;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Lnet/minecraft/block/entity/BlockEntity;)V won't be passed a CallbackInfo as a result
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) fabric-data-attachment-api-v1.mixins.json:WorldChunkMixin from mod fabric-data-attachment-api-v1->@Inject::transferProtoChunkAttachement(Lnet/minecraft/server/world/ServerWorld;Lnet/minecraft/world/chunk/ProtoChunk;Lnet/minecraft/world/chunk/WorldChunk$EntityLoader;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) fabric-data-attachment-api-v1.mixins.json:WorldChunkMixin from mod fabric-data-attachment-api-v1->@Inject::transferProtoChunkAttachement(Lnet/minecraft/server/world/ServerWorld;Lnet/minecraft/world/chunk/ProtoChunk;Lnet/minecraft/world/chunk/WorldChunk$EntityLoader;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) fabric-data-attachment-api-v1.mixins.json:WorldChunkMixin from mod fabric-data-attachment-api-v1->@Inject::transferProtoChunkAttachement(Lnet/minecraft/server/world/ServerWorld;Lnet/minecraft/world/chunk/ProtoChunk;Lnet/minecraft/world/chunk/WorldChunk$EntityLoader;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) Mixing VillagerEntityMixin from fabric-entity-events-v1.mixins.json into net.minecraft.entity.passive.VillagerEntity
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.mixins.json:VillagerEntityMixin from mod fabric-entity-events-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) Mixing VillagerEntityAccessor from fabric-content-registries-v0.mixins.json into net.minecraft.entity.passive.VillagerEntity
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) fabric-content-registries-v0.mixins.json:VillagerEntityAccessor from mod fabric-content-registries-v0: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) Renaming @Accessor method fabric_setItemFoodValues(Ljava/util/Map;)V to fabric_setItemFoodValues$fabric-content-registries-v0_$md$5da3e1$0 in fabric-content-registries-v0.mixins.json:VillagerEntityAccessor from mod fabric-content-registries-v0
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) Renaming @Accessor method fabric_setGatherableItems(Ljava/util/Set;)V to fabric_setGatherableItems$fabric-content-registries-v0_$md$5da3e1$1 in fabric-content-registries-v0.mixins.json:VillagerEntityAccessor from mod fabric-content-registries-v0
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) Renaming @Accessor method fabric_getGatherableItems()Ljava/util/Set; to fabric_getGatherableItems$fabric-content-registries-v0_$md$5da3e1$2 in fabric-content-registries-v0.mixins.json:VillagerEntityAccessor from mod fabric-content-registries-v0
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) Mixing ingredient.IngredientMixin from fabric-recipe-api-v1.mixins.json into net.minecraft.recipe.Ingredient
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) fabric-recipe-api-v1.mixins.json:ingredient.IngredientMixin from mod fabric-recipe-api-v1->@Inject::injectFromJson(Lcom/google/gson/JsonElement;ZLorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) fabric-recipe-api-v1.mixins.json:ingredient.IngredientMixin from mod fabric-recipe-api-v1->@Inject::injectEntryFromJson(Lcom/google/gson/JsonObject;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V doesn't use it's CallbackInfoReturnable
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) fabric-recipe-api-v1.mixins.json:ingredient.IngredientMixin from mod fabric-recipe-api-v1->@Inject::injectFromPacket(Lnet/minecraft/network/PacketByteBuf;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) Mixing ServerPlayNetworkHandlerMixin from fabric-networking-api-v1.mixins.json into net.minecraft.server.network.ServerPlayNetworkHandler
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:ServerPlayNetworkHandlerMixin from mod fabric-networking-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) Mixing accessor.ServerPlayNetworkHandlerAccessor from fabric-networking-api-v1.mixins.json into net.minecraft.server.network.ServerPlayNetworkHandler
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:accessor.ServerPlayNetworkHandlerAccessor from mod fabric-networking-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:ServerPlayNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::initAddon(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:ServerPlayNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::initAddon(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:ServerPlayNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::initAddon(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:ServerPlayNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::handleCustomPayloadReceivedAsync(Lnet/minecraft/network/packet/c2s/play/CustomPayloadC2SPacket;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:ServerPlayNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::handleDisconnection(Lnet/minecraft/text/Text;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:ServerPlayNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::handleDisconnection(Lnet/minecraft/text/Text;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:55] [main/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:ServerPlayNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::handleDisconnection(Lnet/minecraft/text/Text;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:56] [main/DEBUG] (FabricLoader/Mixin) Mixing EntitySelectorOptionsAccessor from fabric-command-api-v2.mixins.json into net.minecraft.command.EntitySelectorOptions
[16:08:56] [main/DEBUG] (FabricLoader/Mixin) fabric-command-api-v2.mixins.json:EntitySelectorOptionsAccessor from mod fabric-command-api-v2: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:56] [main/DEBUG] (FabricLoader/Mixin) Renaming @Invoker method callPutOption(Ljava/lang/String;Lnet/minecraft/command/EntitySelectorOptions$SelectorHandler;Ljava/util/function/Predicate;Lnet/minecraft/text/Text;)V to callPutOption$fabric-command-api-v2_$md$5da3e1$0 in fabric-command-api-v2.mixins.json:EntitySelectorOptionsAccessor from mod fabric-command-api-v2
[16:08:56] [main/DEBUG] (FabricLoader/Mixin) Mixing EntitySelectorReaderMixin from fabric-command-api-v2.mixins.json into net.minecraft.command.EntitySelectorReader
[16:08:56] [main/DEBUG] (FabricLoader/Mixin) fabric-command-api-v2.mixins.json:EntitySelectorReaderMixin from mod fabric-command-api-v2: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:56] [main/DEBUG] (FabricLoader/Mixin) Generating mapped inner class net/minecraft/block/entity/ChiseledBookshelfBlockEntity$Anonymous$499fa590095648b582cba226f057bf5f (originally net/fabricmc/fabric/mixin/transfer/ChiseledBookshelfBlockEntityMixin$1)
[16:08:56] [main/DEBUG] (FabricLoader/Mixin) Mixing StructureTemplateManagerMixin from fabric-gametest-api-v1.mixins.json into net.minecraft.structure.StructureTemplateManager
[16:08:56] [main/DEBUG] (FabricLoader/Mixin) fabric-gametest-api-v1.mixins.json:StructureTemplateManagerMixin from mod fabric-gametest-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:56] [main/DEBUG] (FabricLoader/Mixin) fabric-gametest-api-v1.mixins.json:StructureTemplateManagerMixin from mod fabric-gametest-api-v1->@Inject::addFabricTemplateProvider(Lnet/minecraft/resource/ResourceManager;Lnet/minecraft/world/level/storage/LevelStorage$Session;Lcom/mojang/datafixers/DataFixer;Lnet/minecraft/registry/RegistryEntryLookup;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Lcom/google/common/collect/ImmutableList$Builder;)V doesn't use it's CallbackInfo
[16:08:56] [main/DEBUG] (FabricLoader/Mixin) fabric-gametest-api-v1.mixins.json:StructureTemplateManagerMixin from mod fabric-gametest-api-v1->@Inject::addFabricTemplateProvider(Lnet/minecraft/resource/ResourceManager;Lnet/minecraft/world/level/storage/LevelStorage$Session;Lcom/mojang/datafixers/DataFixer;Lnet/minecraft/registry/RegistryEntryLookup;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Lcom/google/common/collect/ImmutableList$Builder;)V has 0 override(s) in child classes
[16:08:56] [main/DEBUG] (FabricLoader/Mixin) fabric-gametest-api-v1.mixins.json:StructureTemplateManagerMixin from mod fabric-gametest-api-v1->@Inject::addFabricTemplateProvider(Lnet/minecraft/resource/ResourceManager;Lnet/minecraft/world/level/storage/LevelStorage$Session;Lcom/mojang/datafixers/DataFixer;Lnet/minecraft/registry/RegistryEntryLookup;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Lcom/google/common/collect/ImmutableList$Builder;)V won't be passed a CallbackInfo as a result
[16:08:56] [main/DEBUG] (FabricLoader/Mixin) Mixing SimpleInventoryMixin from fabric-transfer-api-v1.mixins.json into net.minecraft.inventory.SimpleInventory
[16:08:56] [main/DEBUG] (FabricLoader/Mixin) fabric-transfer-api-v1.mixins.json:SimpleInventoryMixin from mod fabric-transfer-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:56] [main/DEBUG] (FabricLoader/Mixin) Mixing RecipeMixin from fabric-item-api-v1.mixins.json into net.minecraft.recipe.Recipe
[16:08:56] [main/DEBUG] (FabricLoader/Mixin) fabric-item-api-v1.mixins.json:RecipeMixin from mod fabric-item-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:56] [main/DEBUG] (FabricLoader/Mixin) fabric-item-api-v1.mixins.json:RecipeMixin from mod fabric-item-api-v1->@Inject::captureStack(Lnet/minecraft/inventory/Inventory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;Lnet/minecraft/util/collection/DefaultedList;I)V doesn't use it's CallbackInfoReturnable
[16:08:56] [main/DEBUG] (FabricLoader/Mixin) fabric-item-api-v1.mixins.json:RecipeMixin from mod fabric-item-api-v1->@Inject::captureStack(Lnet/minecraft/inventory/Inventory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;Lnet/minecraft/util/collection/DefaultedList;I)V has 0 override(s) in child classes
[16:08:56] [main/DEBUG] (FabricLoader/Mixin) fabric-item-api-v1.mixins.json:RecipeMixin from mod fabric-item-api-v1->@Inject::captureStack(Lnet/minecraft/inventory/Inventory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;Lnet/minecraft/util/collection/DefaultedList;I)V won't be passed a CallbackInfoReturnable as a result
[16:08:56] [main/DEBUG] (FabricLoader/Mixin) Mixing RegistryCodecsMixin from fabric-dimensions-v1.mixins.json into net.minecraft.registry.RegistryCodecs
[16:08:56] [main/DEBUG] (FabricLoader/Mixin) fabric-dimensions-v1.mixins.json:RegistryCodecsMixin from mod fabric-dimensions-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:56] [main/DEBUG] (FabricLoader/Mixin) Mixing BiomeSourceMixin from fabric-biome-api-v1.mixins.json into net.minecraft.world.biome.source.BiomeSource
[16:08:56] [main/DEBUG] (FabricLoader/Mixin) fabric-biome-api-v1.mixins.json:BiomeSourceMixin from mod fabric-biome-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:57] [main/DEBUG] (FabricLoader/Mixin) Mixing MultiNoiseBiomeSourceMixin from fabric-biome-api-v1.mixins.json into net.minecraft.world.biome.source.MultiNoiseBiomeSource
[16:08:57] [main/DEBUG] (FabricLoader/Mixin) fabric-biome-api-v1.mixins.json:MultiNoiseBiomeSourceMixin from mod fabric-biome-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:57] [main/DEBUG] (FabricLoader/Mixin) Mixing NetherBiomePresetMixin from fabric-biome-api-v1.mixins.json into net.minecraft.world.biome.source.MultiNoiseBiomeSourceParameterList$Preset$1
[16:08:57] [main/DEBUG] (FabricLoader/Mixin) fabric-biome-api-v1.mixins.json:NetherBiomePresetMixin from mod fabric-biome-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:57] [main/DEBUG] (FabricLoader/Mixin) fabric-biome-api-v1.mixins.json:NetherBiomePresetMixin from mod fabric-biome-api-v1->@Inject::apply(Ljava/util/function/Function;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[16:08:57] [main/DEBUG] (FabricLoader/Mixin) Mixing TheEndBiomeSourceMixin from fabric-biome-api-v1.mixins.json into net.minecraft.world.biome.source.TheEndBiomeSource
[16:08:57] [main/DEBUG] (FabricLoader/Mixin) fabric-biome-api-v1.mixins.json:TheEndBiomeSourceMixin from mod fabric-biome-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:57] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$init$1(Lnet/minecraft/registry/RegistryEntryLookup;)Lnet/fabricmc/fabric/impl/biome/TheEndBiomeData$Overrides; to md5da3e1$fabric-biome-api-v1$lambda$init$1$0 in fabric-biome-api-v1.mixins.json:TheEndBiomeSourceMixin from mod fabric-biome-api-v1
[16:08:57] [main/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$modifyCodec$0(Lcom/mojang/serialization/codecs/RecordCodecBuilder$Instance;)Lcom/mojang/datafixers/kinds/App; to md5da3e1$fabric-biome-api-v1$lambda$modifyCodec$0$1 in fabric-biome-api-v1.mixins.json:TheEndBiomeSourceMixin from mod fabric-biome-api-v1
[16:08:57] [main/DEBUG] (FabricLoader/Mixin) fabric-biome-api-v1.mixins.json:TheEndBiomeSourceMixin from mod fabric-biome-api-v1->@Inject::modifyCodec(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:57] [main/DEBUG] (FabricLoader/Mixin) fabric-biome-api-v1.mixins.json:TheEndBiomeSourceMixin from mod fabric-biome-api-v1->@Inject::rememberLookup(Lnet/minecraft/registry/RegistryEntryLookup;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V doesn't use it's CallbackInfoReturnable
[16:08:57] [main/DEBUG] (FabricLoader/Mixin) fabric-biome-api-v1.mixins.json:TheEndBiomeSourceMixin from mod fabric-biome-api-v1->@Inject::clearLookup(Lnet/minecraft/registry/RegistryEntryLookup;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V doesn't use it's CallbackInfoReturnable
[16:08:57] [main/DEBUG] (FabricLoader/Mixin) fabric-biome-api-v1.mixins.json:TheEndBiomeSourceMixin from mod fabric-biome-api-v1->@Inject::init(Lnet/minecraft/registry/entry/RegistryEntry;Lnet/minecraft/registry/entry/RegistryEntry;Lnet/minecraft/registry/entry/RegistryEntry;Lnet/minecraft/registry/entry/RegistryEntry;Lnet/minecraft/registry/entry/RegistryEntry;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:57] [main/DEBUG] (FabricLoader/Mixin) fabric-biome-api-v1.mixins.json:TheEndBiomeSourceMixin from mod fabric-biome-api-v1->@Inject::init(Lnet/minecraft/registry/entry/RegistryEntry;Lnet/minecraft/registry/entry/RegistryEntry;Lnet/minecraft/registry/entry/RegistryEntry;Lnet/minecraft/registry/entry/RegistryEntry;Lnet/minecraft/registry/entry/RegistryEntry;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:57] [main/DEBUG] (FabricLoader/Mixin) fabric-biome-api-v1.mixins.json:TheEndBiomeSourceMixin from mod fabric-biome-api-v1->@Inject::init(Lnet/minecraft/registry/entry/RegistryEntry;Lnet/minecraft/registry/entry/RegistryEntry;Lnet/minecraft/registry/entry/RegistryEntry;Lnet/minecraft/registry/entry/RegistryEntry;Lnet/minecraft/registry/entry/RegistryEntry;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:57] [main/DEBUG] (FabricLoader/Mixin) fabric-biome-api-v1.mixins.json:TheEndBiomeSourceMixin from mod fabric-biome-api-v1->@Inject::getWeightedEndBiome(IIILnet/minecraft/world/biome/source/util/MultiNoiseUtil$MultiNoiseSampler;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[16:08:57] [main/DEBUG] (FabricLoader/Mixin) fabric-biome-api-v1.mixins.json:TheEndBiomeSourceMixin from mod fabric-biome-api-v1->@Inject::getWeightedEndBiome(IIILnet/minecraft/world/biome/source/util/MultiNoiseUtil$MultiNoiseSampler;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[16:08:57] [main/DEBUG] (FabricLoader/Mixin) fabric-biome-api-v1.mixins.json:TheEndBiomeSourceMixin from mod fabric-biome-api-v1->@Inject::getWeightedEndBiome(IIILnet/minecraft/world/biome/source/util/MultiNoiseUtil$MultiNoiseSampler;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[16:08:57] [main/DEBUG] (FabricLoader/Mixin) fabric-biome-api-v1.mixins.json:TheEndBiomeSourceMixin from mod fabric-biome-api-v1->@Inject::getWeightedEndBiome(IIILnet/minecraft/world/biome/source/util/MultiNoiseUtil$MultiNoiseSampler;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[16:08:57] [main/DEBUG] (FabricLoader/Mixin) fabric-biome-api-v1.mixins.json:TheEndBiomeSourceMixin from mod fabric-biome-api-v1->@Inject::getWeightedEndBiome(IIILnet/minecraft/world/biome/source/util/MultiNoiseUtil$MultiNoiseSampler;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[16:08:57] [main/DEBUG] (FabricLoader/Mixin) Mixing DebugChunkGeneratorAccessor from fabric-registry-sync-v0.mixins.json into net.minecraft.world.gen.chunk.DebugChunkGenerator
[16:08:57] [main/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.mixins.json:DebugChunkGeneratorAccessor from mod fabric-registry-sync-v0: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:57] [main/DEBUG] (FabricLoader/Mixin) Renaming @Accessor method setBLOCK_STATES(Ljava/util/List;)V to setBLOCK_STATES$fabric-registry-sync-v0_$md$5da3e1$0 in fabric-registry-sync-v0.mixins.json:DebugChunkGeneratorAccessor from mod fabric-registry-sync-v0
[16:08:57] [main/DEBUG] (FabricLoader/Mixin) Renaming @Accessor method setX_SIDE_LENGTH(I)V to setX_SIDE_LENGTH$fabric-registry-sync-v0_$md$5da3e1$1 in fabric-registry-sync-v0.mixins.json:DebugChunkGeneratorAccessor from mod fabric-registry-sync-v0
[16:08:57] [main/DEBUG] (FabricLoader/Mixin) Renaming @Accessor method setZ_SIDE_LENGTH(I)V to setZ_SIDE_LENGTH$fabric-registry-sync-v0_$md$5da3e1$2 in fabric-registry-sync-v0.mixins.json:DebugChunkGeneratorAccessor from mod fabric-registry-sync-v0
[16:08:57] [main/DEBUG] (FabricLoader/Mixin) Generating mapped inner class net/minecraft/item/ItemGroups$1ItemGroupPosition$529e5bdc5efb4878a1d594708678fad9 (originally net/fabricmc/fabric/mixin/itemgroup/ItemGroupsMixin$1ItemGroupPosition)
[16:08:57] [main/DEBUG] (FabricLoader/Mixin) Mixing DefaultAttributeRegistryAccessor from fabric-object-builder-v1.mixins.json into net.minecraft.entity.attribute.DefaultAttributeRegistry
[16:08:57] [main/DEBUG] (FabricLoader/Mixin) fabric-object-builder-v1.mixins.json:DefaultAttributeRegistryAccessor from mod fabric-object-builder-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:57] [main/DEBUG] (FabricLoader/Mixin) Renaming @Accessor method getRegistry()Ljava/util/Map; to getRegistry$fabric-object-builder-api-v1_$md$5da3e1$0 in fabric-object-builder-v1.mixins.json:DefaultAttributeRegistryAccessor from mod fabric-object-builder-api-v1
[16:08:57] [main/DEBUG] (FabricLoader/Mixin) Mixing DefaultAttributeRegistryMixin from fabric-object-builder-v1.mixins.json into net.minecraft.entity.attribute.DefaultAttributeRegistry
[16:08:57] [main/DEBUG] (FabricLoader/Mixin) fabric-object-builder-v1.mixins.json:DefaultAttributeRegistryMixin from mod fabric-object-builder-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:57] [main/DEBUG] (FabricLoader/Mixin) fabric-object-builder-v1.mixins.json:DefaultAttributeRegistryMixin from mod fabric-object-builder-api-v1->@Inject::injectAttributes(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing MinecraftClientMixin from pokecobbleclaim.mixins.json into net.minecraft.client.MinecraftClient
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing MinecraftClientMixin from fabric-lifecycle-events-v1.client.mixins.json into net.minecraft.client.MinecraftClient
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:MinecraftClientMixin from mod fabric-lifecycle-events-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing MinecraftClientMixin from fabric-screen-api-v1.mixins.json into net.minecraft.client.MinecraftClient
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:MinecraftClientMixin from mod fabric-screen-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) Renaming @Unique field LOGGERLorg/slf4j/Logger; to fd5da3e1$fabric-screen-api-v1$LOGGER$0 in fabric-screen-api-v1.mixins.json:MinecraftClientMixin from mod fabric-screen-api-v1
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing MinecraftClientMixin from fabric-registry-sync-v0.client.mixins.json into net.minecraft.client.MinecraftClient
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.client.mixins.json:MinecraftClientMixin from mod fabric-registry-sync-v0: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing accessor.MinecraftClientAccessor from fabric-networking-api-v1.client.mixins.json into net.minecraft.client.MinecraftClient
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.client.mixins.json:accessor.MinecraftClientAccessor from mod fabric-networking-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing MinecraftClientMixin from fabric-data-generation-api-v1.client.mixins.json into net.minecraft.client.MinecraftClient
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-data-generation-api-v1.client.mixins.json:MinecraftClientMixin from mod fabric-data-generation-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing MinecraftClientMixin from fabric-events-interaction-v0.client.mixins.json into net.minecraft.client.MinecraftClient
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-events-interaction-v0.client.mixins.json:MinecraftClientMixin from mod fabric-events-interaction-v0: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) pokecobbleclaim.mixins.json:MinecraftClientMixin from mod pokecobbleclaim->@Inject::onSetScreen(Lnet/minecraft/client/gui/screen/Screen;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:MinecraftClientMixin from mod fabric-lifecycle-events-v1->@Inject::onStartTick(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:MinecraftClientMixin from mod fabric-lifecycle-events-v1->@Inject::onStartTick(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:MinecraftClientMixin from mod fabric-lifecycle-events-v1->@Inject::onStartTick(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:MinecraftClientMixin from mod fabric-lifecycle-events-v1->@Inject::onEndTick(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:MinecraftClientMixin from mod fabric-lifecycle-events-v1->@Inject::onEndTick(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:MinecraftClientMixin from mod fabric-lifecycle-events-v1->@Inject::onEndTick(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:MinecraftClientMixin from mod fabric-lifecycle-events-v1->@Inject::onStopping(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:MinecraftClientMixin from mod fabric-lifecycle-events-v1->@Inject::onStopping(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:MinecraftClientMixin from mod fabric-lifecycle-events-v1->@Inject::onStopping(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:MinecraftClientMixin from mod fabric-lifecycle-events-v1->@Inject::onStart(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:MinecraftClientMixin from mod fabric-lifecycle-events-v1->@Inject::onStart(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:MinecraftClientMixin from mod fabric-lifecycle-events-v1->@Inject::onStart(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:MinecraftClientMixin from mod fabric-screen-api-v1->@Inject::checkThreadOnDev(Lnet/minecraft/client/gui/screen/Screen;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:MinecraftClientMixin from mod fabric-screen-api-v1->@Inject::checkThreadOnDev(Lnet/minecraft/client/gui/screen/Screen;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:MinecraftClientMixin from mod fabric-screen-api-v1->@Inject::checkThreadOnDev(Lnet/minecraft/client/gui/screen/Screen;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:MinecraftClientMixin from mod fabric-screen-api-v1->@Inject::onScreenRemove(Lnet/minecraft/client/gui/screen/Screen;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:MinecraftClientMixin from mod fabric-screen-api-v1->@Inject::onScreenRemove(Lnet/minecraft/client/gui/screen/Screen;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:MinecraftClientMixin from mod fabric-screen-api-v1->@Inject::onScreenRemove(Lnet/minecraft/client/gui/screen/Screen;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:MinecraftClientMixin from mod fabric-screen-api-v1->@Inject::onScreenRemoveBecauseStopping(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:MinecraftClientMixin from mod fabric-screen-api-v1->@Inject::onScreenRemoveBecauseStopping(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:MinecraftClientMixin from mod fabric-screen-api-v1->@Inject::onScreenRemoveBecauseStopping(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:MinecraftClientMixin from mod fabric-screen-api-v1->@Inject::beforeScreenTick(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:MinecraftClientMixin from mod fabric-screen-api-v1->@Inject::beforeScreenTick(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:MinecraftClientMixin from mod fabric-screen-api-v1->@Inject::beforeScreenTick(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:MinecraftClientMixin from mod fabric-screen-api-v1->@Inject::afterScreenTick(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:MinecraftClientMixin from mod fabric-screen-api-v1->@Inject::afterScreenTick(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:MinecraftClientMixin from mod fabric-screen-api-v1->@Inject::afterScreenTick(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:MinecraftClientMixin from mod fabric-screen-api-v1->@Inject::beforeLoadingScreenTick(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:MinecraftClientMixin from mod fabric-screen-api-v1->@Inject::beforeLoadingScreenTick(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:MinecraftClientMixin from mod fabric-screen-api-v1->@Inject::beforeLoadingScreenTick(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:MinecraftClientMixin from mod fabric-screen-api-v1->@Inject::afterLoadingScreenTick(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:MinecraftClientMixin from mod fabric-screen-api-v1->@Inject::afterLoadingScreenTick(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:MinecraftClientMixin from mod fabric-screen-api-v1->@Inject::afterLoadingScreenTick(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.client.mixins.json:MinecraftClientMixin from mod fabric-registry-sync-v0->@Inject::disconnectAfter(Lnet/minecraft/client/gui/screen/Screen;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.client.mixins.json:MinecraftClientMixin from mod fabric-registry-sync-v0->@Inject::disconnectAfter(Lnet/minecraft/client/gui/screen/Screen;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.client.mixins.json:MinecraftClientMixin from mod fabric-registry-sync-v0->@Inject::disconnectAfter(Lnet/minecraft/client/gui/screen/Screen;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.client.mixins.json:MinecraftClientMixin from mod fabric-registry-sync-v0->@Inject::onStart(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.client.mixins.json:MinecraftClientMixin from mod fabric-registry-sync-v0->@Inject::onStart(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.client.mixins.json:MinecraftClientMixin from mod fabric-registry-sync-v0->@Inject::onStart(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-data-generation-api-v1.client.mixins.json:MinecraftClientMixin from mod fabric-data-generation-api-v1->@Inject::main(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-data-generation-api-v1.client.mixins.json:MinecraftClientMixin from mod fabric-data-generation-api-v1->@Inject::main(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-data-generation-api-v1.client.mixins.json:MinecraftClientMixin from mod fabric-data-generation-api-v1->@Inject::main(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-events-interaction-v0.client.mixins.json:MinecraftClientMixin from mod fabric-events-interaction-v0->@Inject::fabric_doItemPickWrapper(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-events-interaction-v0.client.mixins.json:MinecraftClientMixin from mod fabric-events-interaction-v0->@Inject::cancelItemPick(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-events-interaction-v0.client.mixins.json:MinecraftClientMixin from mod fabric-events-interaction-v0->@Inject::injectUseEntityCallback(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;[Lnet/minecraft/util/Hand;IILnet/minecraft/util/Hand;Lnet/minecraft/item/ItemStack;Lnet/minecraft/util/hit/EntityHitResult;Lnet/minecraft/entity/Entity;)V does use it's CallbackInfo
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-events-interaction-v0.client.mixins.json:MinecraftClientMixin from mod fabric-events-interaction-v0->@Inject::injectHandleInputEventsForPreAttackCallback(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-events-interaction-v0.client.mixins.json:MinecraftClientMixin from mod fabric-events-interaction-v0->@Inject::injectHandleInputEventsForPreAttackCallback(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-events-interaction-v0.client.mixins.json:MinecraftClientMixin from mod fabric-events-interaction-v0->@Inject::injectHandleInputEventsForPreAttackCallback(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-events-interaction-v0.client.mixins.json:MinecraftClientMixin from mod fabric-events-interaction-v0->@Inject::injectDoAttackForCancelling(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-events-interaction-v0.client.mixins.json:MinecraftClientMixin from mod fabric-events-interaction-v0->@Inject::injectHandleBlockBreakingForCancelling(ZLorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing ScreenMixin from pokecobbleclaim.mixins.json into net.minecraft.client.gui.screen.Screen
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing ScreenAccessor from fabric-screen-api-v1.mixins.json into net.minecraft.client.gui.screen.Screen
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:ScreenAccessor from mod fabric-screen-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing ScreenMixin from fabric-screen-api-v1.mixins.json into net.minecraft.client.gui.screen.Screen
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:ScreenMixin from mod fabric-screen-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) pokecobbleclaim.mixins.json:ScreenMixin from mod pokecobbleclaim->@Inject::onScreenInit(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) pokecobbleclaim.mixins.json:ScreenMixin from mod pokecobbleclaim->@Inject::onScreenInit(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) pokecobbleclaim.mixins.json:ScreenMixin from mod pokecobbleclaim->@Inject::onScreenInit(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) pokecobbleclaim.mixins.json:ScreenMixin from mod pokecobbleclaim->@Inject::onScreenRemoved(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) pokecobbleclaim.mixins.json:ScreenMixin from mod pokecobbleclaim->@Inject::onScreenRemoved(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) pokecobbleclaim.mixins.json:ScreenMixin from mod pokecobbleclaim->@Inject::onScreenRemoved(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:ScreenMixin from mod fabric-screen-api-v1->@Inject::beforeInitScreen(Lnet/minecraft/client/MinecraftClient;IILorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:ScreenMixin from mod fabric-screen-api-v1->@Inject::beforeInitScreen(Lnet/minecraft/client/MinecraftClient;IILorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:ScreenMixin from mod fabric-screen-api-v1->@Inject::beforeInitScreen(Lnet/minecraft/client/MinecraftClient;IILorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:ScreenMixin from mod fabric-screen-api-v1->@Inject::afterInitScreen(Lnet/minecraft/client/MinecraftClient;IILorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:ScreenMixin from mod fabric-screen-api-v1->@Inject::afterInitScreen(Lnet/minecraft/client/MinecraftClient;IILorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:ScreenMixin from mod fabric-screen-api-v1->@Inject::afterInitScreen(Lnet/minecraft/client/MinecraftClient;IILorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:ScreenMixin from mod fabric-screen-api-v1->@Inject::beforeResizeScreen(Lnet/minecraft/client/MinecraftClient;IILorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:ScreenMixin from mod fabric-screen-api-v1->@Inject::beforeResizeScreen(Lnet/minecraft/client/MinecraftClient;IILorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:ScreenMixin from mod fabric-screen-api-v1->@Inject::beforeResizeScreen(Lnet/minecraft/client/MinecraftClient;IILorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:ScreenMixin from mod fabric-screen-api-v1->@Inject::afterResizeScreen(Lnet/minecraft/client/MinecraftClient;IILorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:ScreenMixin from mod fabric-screen-api-v1->@Inject::afterResizeScreen(Lnet/minecraft/client/MinecraftClient;IILorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:ScreenMixin from mod fabric-screen-api-v1->@Inject::afterResizeScreen(Lnet/minecraft/client/MinecraftClient;IILorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing ClientWorldMixin from fabric-lifecycle-events-v1.client.mixins.json into net.minecraft.client.world.ClientWorld
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:ClientWorldMixin from mod fabric-lifecycle-events-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:ClientWorldMixin from mod fabric-lifecycle-events-v1->@Inject::tickWorldAfterBlockEntities(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:ClientWorldMixin from mod fabric-lifecycle-events-v1->@Inject::tickWorldAfterBlockEntities(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:ClientWorldMixin from mod fabric-lifecycle-events-v1->@Inject::tickWorldAfterBlockEntities(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:ClientWorldMixin from mod fabric-lifecycle-events-v1->@Inject::startWorldTick(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:ClientWorldMixin from mod fabric-lifecycle-events-v1->@Inject::startWorldTick(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:ClientWorldMixin from mod fabric-lifecycle-events-v1->@Inject::startWorldTick(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing elytra.ClientPlayerEntityMixin from fabric-entity-events-v1.client.mixins.json into net.minecraft.client.network.ClientPlayerEntity
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.client.mixins.json:elytra.ClientPlayerEntityMixin from mod fabric-entity-events-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.client.mixins.json:elytra.ClientPlayerEntityMixin from mod fabric-entity-events-v1->@Inject::injectElytraStart(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.client.mixins.json:elytra.ClientPlayerEntityMixin from mod fabric-entity-events-v1->@Inject::injectElytraStart(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-entity-events-v1.client.mixins.json:elytra.ClientPlayerEntityMixin from mod fabric-entity-events-v1->@Inject::injectElytraStart(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing SinglePreparationResourceReloaderMixin from fabric-resource-conditions-api-v1.mixins.json into net.minecraft.resource.SinglePreparationResourceReloader
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-conditions-api-v1.mixins.json:SinglePreparationResourceReloaderMixin from mod fabric-resource-conditions-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-conditions-api-v1.mixins.json:SinglePreparationResourceReloaderMixin from mod fabric-resource-conditions-api-v1->@Inject::applyResourceConditions(Lnet/minecraft/resource/ResourceManager;Lnet/minecraft/util/profiler/Profiler;Ljava/lang/Object;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-conditions-api-v1.mixins.json:SinglePreparationResourceReloaderMixin from mod fabric-resource-conditions-api-v1->@Inject::applyResourceConditions(Lnet/minecraft/resource/ResourceManager;Lnet/minecraft/util/profiler/Profiler;Ljava/lang/Object;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-conditions-api-v1.mixins.json:SinglePreparationResourceReloaderMixin from mod fabric-resource-conditions-api-v1->@Inject::applyResourceConditions(Lnet/minecraft/resource/ResourceManager;Lnet/minecraft/util/profiler/Profiler;Ljava/lang/Object;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing DefaultClientResourcePackProviderMixin from fabric-resource-loader-v0.client.mixins.json into net.minecraft.client.resource.DefaultClientResourcePackProvider
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.client.mixins.json:DefaultClientResourcePackProviderMixin from mod fabric-resource-loader-v0: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$onCreateVanillaBuiltinResourcePack$0(Lnet/minecraft/resource/ResourcePackProfile$PackFactory;Ljava/lang/String;Ljava/lang/String;)Lnet/minecraft/resource/ResourcePack; to md5da3e1$fabric-resource-loader-v0$lambda$onCreateVanillaBuiltinResourcePack$0$0 in fabric-resource-loader-v0.client.mixins.json:DefaultClientResourcePackProviderMixin from mod fabric-resource-loader-v0
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing VanillaResourcePackProviderMixin from fabric-resource-loader-v0.client.mixins.json into net.minecraft.resource.VanillaResourcePackProvider
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.client.mixins.json:VanillaResourcePackProviderMixin from mod fabric-resource-loader-v0: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.client.mixins.json:VanillaResourcePackProviderMixin from mod fabric-resource-loader-v0->@Inject::addBuiltinResourcePacks(Ljava/util/function/Consumer;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.client.mixins.json:VanillaResourcePackProviderMixin from mod fabric-resource-loader-v0->@Inject::addBuiltinResourcePacks(Ljava/util/function/Consumer;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.client.mixins.json:VanillaResourcePackProviderMixin from mod fabric-resource-loader-v0->@Inject::addBuiltinResourcePacks(Ljava/util/function/Consumer;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing ResourcePackManagerMixin from fabric-resource-loader-v0.mixins.json into net.minecraft.resource.ResourcePackManager
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.mixins.json:ResourcePackManagerMixin from mod fabric-resource-loader-v0: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.mixins.json:ResourcePackManagerMixin from mod fabric-resource-loader-v0->@Inject::construct([Lnet/minecraft/resource/ResourcePackProvider;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.mixins.json:ResourcePackManagerMixin from mod fabric-resource-loader-v0->@Inject::construct([Lnet/minecraft/resource/ResourcePackProvider;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:57] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.mixins.json:ResourcePackManagerMixin from mod fabric-resource-loader-v0->@Inject::construct([Lnet/minecraft/resource/ResourcePackProvider;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:57] [Render thread/INFO] (com.mojang.authlib.yggdrasil.YggdrasilAuthenticationService) Environment: authHost='https://authserver.mojang.com', accountsHost='https://api.mojang.com', sessionHost='https://sessionserver.mojang.com', servicesHost='https://api.minecraftservices.com', name='PROD'
[16:08:57] [Yggdrasil Key Fetcher/DEBUG] (com.mojang.authlib.HttpAuthenticationService) Opening connection to https://api.minecraftservices.com/publickeys
[16:08:57] [Render thread/DEBUG] (com.mojang.authlib.minecraft.client.MinecraftClient) Connecting to https://api.minecraftservices.com/player/attributes
[16:08:57] [Yggdrasil Key Fetcher/DEBUG] (com.mojang.authlib.HttpAuthenticationService) Reading data from https://api.minecraftservices.com/publickeys
[16:08:58] [Yggdrasil Key Fetcher/DEBUG] (com.mojang.authlib.HttpAuthenticationService) Successful read, server response was 200
[16:08:58] [Yggdrasil Key Fetcher/DEBUG] (com.mojang.authlib.HttpAuthenticationService) Response: {
  "profilePropertyKeys" : [ {
    "publicKey" : "MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEAylB4B6m5lz7jwrcFz6Fd/fnfUhcvlxsTSn5kIK/2aGG1C3kMy4VjhwlxF6BFUSnfxhNswPjh3ZitkBxEAFY25uzkJFRwHwVA9mdwjashXILtR6OqdLXXFVyUPIURLOSWqGNBtb08EN5fMnG8iFLgEJIBMxs9BvF3s3/FhuHyPKiVTZmXY0WY4ZyYqvoKR+XjaTRPPvBsDa4WI2u1zxXMeHlodT3lnCzVvyOYBLXL6CJgByuOxccJ8hnXfF9yY4F0aeL080Jz/3+EBNG8RO4ByhtBf4Ny8NQ6stWsjfeUIvH7bU/4zCYcYOq4WrInXHqS8qruDmIl7P5XXGcabuzQstPf/h2CRAUpP/PlHXcMlvewjmGU6MfDK+lifScNYwjPxRo4nKTGFZf/0aqHCh/EAsQyLKrOIYRE0lDG3bzBh8ogIMLAugsAfBb6M3mqCqKaTMAf/VAjh5FFJnjS+7bE+bZEV0qwax1CEoPPJL1fIQjOS8zj086gjpGRCtSy9+bTPTfTR/SJ+VUB5G2IeCItkNHpJX2ygojFZ9n5Fnj7R9ZnOM+L8nyIjPu3aePvtcrXlyLhH/hvOfIOjPxOlqW+O5QwSFP4OEcyLAUgDdUgyW36Z5mB285uKW/ighzZsOTevVUG2QwDItObIV6i8RCxFbN2oDHyPaO5j1tTaBNyVt8CAwEAAQ=="
  }, {
    "publicKey" : "MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEAra4Y2wu3rWEW7cDTDRRd4IvUD140Y12SaG3k4V3UwT/pDnnX5itOcYiZA0qf4VCpJDp2PifOL+Pr/ph/G9/6ZoIxkBeGENo+S7i9BqizJy9cmZocpyx+RkZaw9+frCGNLuYLrxziNWiXFACJSg2mHACR7+6NkGN8d/16/3PxMnvGSyLT7JKGUgqj1Q3oW7k+NLXR9sw6oRELOcnUvZVa2bcglv8vlcyPqqnBhydLfHI85Z5WnIYZviZ3Bb4dv5Fme726BGOtEY7kz40RfiwjT3xYKYKPJUS3/crPX6eugmWyrWdddKaePrW88bp17Z5NIStlJ5KJJk4coha8O+P7onDqmbHwLqPTeR51njkgZ+DJWT6fz8ku9OWQn6I/FxqN14iYIghDJijmKvEwsI7FJ5X2ttPXEvBYLmpj2j0lQQcUIqH7hkiZ+mCW0GYawJgbAeNAraM9sP+76MyAGITtAsXv1IQmah+7OeDJOToG2Kb1Dl0Va+HiP9MPpcnO7kbn6dqAyhNvRNmHnsUOiEcLhW9Rk7xz87IBV/cGKbUDgxu8cYY0P512DWt5+Jmr8W10FDFdLmkJt1taWxNxApM2CiFPCimk02koyLZDW9nqpWNw6qS/TOYPdz438qEuamtYUJ+u6WhBjK8xAJEAt5k3gDKX+nlTiG3N6se09D62fS8CAwEAAQ=="
  } ],
  "playerCertificateKeys" : [ {
    "publicKey" : "MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEAylB4B6m5lz7jwrcFz6Fd/fnfUhcvlxsTSn5kIK/2aGG1C3kMy4VjhwlxF6BFUSnfxhNswPjh3ZitkBxEAFY25uzkJFRwHwVA9mdwjashXILtR6OqdLXXFVyUPIURLOSWqGNBtb08EN5fMnG8iFLgEJIBMxs9BvF3s3/FhuHyPKiVTZmXY0WY4ZyYqvoKR+XjaTRPPvBsDa4WI2u1zxXMeHlodT3lnCzVvyOYBLXL6CJgByuOxccJ8hnXfF9yY4F0aeL080Jz/3+EBNG8RO4ByhtBf4Ny8NQ6stWsjfeUIvH7bU/4zCYcYOq4WrInXHqS8qruDmIl7P5XXGcabuzQstPf/h2CRAUpP/PlHXcMlvewjmGU6MfDK+lifScNYwjPxRo4nKTGFZf/0aqHCh/EAsQyLKrOIYRE0lDG3bzBh8ogIMLAugsAfBb6M3mqCqKaTMAf/VAjh5FFJnjS+7bE+bZEV0qwax1CEoPPJL1fIQjOS8zj086gjpGRCtSy9+bTPTfTR/SJ+VUB5G2IeCItkNHpJX2ygojFZ9n5Fnj7R9ZnOM+L8nyIjPu3aePvtcrXlyLhH/hvOfIOjPxOlqW+O5QwSFP4OEcyLAUgDdUgyW36Z5mB285uKW/ighzZsOTevVUG2QwDItObIV6i8RCxFbN2oDHyPaO5j1tTaBNyVt8CAwEAAQ=="
  }, {
    "publicKey" : "MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEAt4t9NPuu7cktclnaH7eZj0omkLcJHeLz5MKsyJEntHZ0INtuBjSSul3Pp3pBeJN8k3ADdcdBLUN90bcAi7WsQqTx3Ft363q3W7TbM8j2iTEdp/0uVspoRt/DP1tkaWFs/w2WwUv9jbVoBUzfUc4pSTIxRwdjmqjZQfvjwKNDbOx3IhP2H0WXodbISejPi1wBZqNW4m1rnZAXp/EpUguxA8mobCa4vUCBkyFDyXdl69/wUSJHyCPmgcMJ364OlAhIqtwVPShBZObvrK/f0BYk6ShJD3N7TFDatSYsIIdcTKRknaIm91s+EsMrdB9U4Yw+ZJ/pyCB4S3vk8zfDCnb0DWIxYH3/EMzaxl77djmTmMzi/JDITup5z3jfWtRZmrAhU2/+W5IO5hEpo3/bCS9PXIY5xb41Lmp2ZO8dXKtyD66Chchy0W129n8vPl2GIruOdrxsjZAHnneyAb9jm0uaGaphwnEnuecX/qgHY6ZMtayvLLsPst8PO6R1vufMy8WqjK+j7LnC1krL7CPDg0NEhyQTmw5l+NCNjSlvB1juM9V4PARg0bYCOkGXm7ydRCjSSH8CJXZpwnd5cBB5WKAX3KPzutRgMi/LFwNSMZzFuUyXaYOZPpD259yqph1LmGqegEdDriACVU+dVEONFMm8eIuBofe7ljmsAFKW9BINwK0CAwEAAQ=="
  } ],
  "authenticationKeys" : [ {
    "publicKey" : "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA4S4G8fJJw4hLRME8zM2aBqaQRqYs0TlU8N+sZl0MUgtyFw0KAXnzbOPH7yu6qbqsGYVKF/D+fUg+49PhFmxEsylvmj/yeRwdp3yFGME/BVfL06zFmAw+rGaxqubcswzIO8ByUtjQOmlnrzj4zvhNSYJmwNbTIKrKNlSHYvYZbUDRquH9yMDOKnvGAMMDGttFrM300mVznRgaTEzU9aPqMvj0YxtxUcGIQTar0TBQa7NzEAr59u5VVx5s6naS6QVBrMc6e32f38enVkNFZQT87KlCb2B6ziPmbaRzWWs4qcHsHz8BUCKplo5iu/ePtwaa5AaVT27Lnv+KzS46eyf1CwIDAQAB"
  }, {
    "publicKey" : "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAys5/Z+MBAOBcdnk3wKDDvsw6UUdTjSRQ+e5Ug2HJnshWunZMQDPGZvctUWpHxDZHAjaFfhpjw1pWtN/ejTpv37jZCp0yF533LfAfxiiQBWfJdKk5Wzyw7kmU8xmO984csukYFH4aTTLwZuhmMLFk3l00mMNPixgnRMuyr7aKIu/+l3wH1kCf1k74MTH4wX5fgNqFvTS3127DNVnTH9sOw+dhEViiQpTz3BFEpIUvl9T7B2rjF0CDmW9xtyNINw2EfENa7PwE0uIyNoZl/+m7yzMKef4lrE6Ch/IMzfT03Q2QvbwFlm+kzQKhSlB18Ohotrkega62fMxdn/s6Rv6oQQIDAQAB"
  } ]
}
[16:08:58] [Render thread/ERROR] (net.minecraft.client.MinecraftClient) Failed to verify authentication
com.mojang.authlib.exceptions.InvalidCredentialsException: Status: 401
	at com.mojang.authlib.exceptions.MinecraftClientHttpException.toAuthenticationException(MinecraftClientHttpException.java:56) ~[authlib-4.0.43.jar:?]
	at com.mojang.authlib.yggdrasil.YggdrasilUserApiService.fetchProperties(YggdrasilUserApiService.java:156) ~[authlib-4.0.43.jar:?]
	at com.mojang.authlib.yggdrasil.YggdrasilUserApiService.<init>(YggdrasilUserApiService.java:55) ~[authlib-4.0.43.jar:?]
	at com.mojang.authlib.yggdrasil.YggdrasilAuthenticationService.createUserApiService(YggdrasilAuthenticationService.java:124) ~[authlib-4.0.43.jar:?]
	at net.minecraft.client.MinecraftClient.createUserApiService(MinecraftClient.java:733) ~[minecraft-merged-7787b014d4-1.20.1-net.fabricmc.yarn.1_20_1.1.20.1+build.10-v2.jar:?]
	at net.minecraft.client.MinecraftClient.<init>(MinecraftClient.java:442) ~[minecraft-merged-7787b014d4-1.20.1-net.fabricmc.yarn.1_20_1.1.20.1+build.10-v2.jar:?]
	at net.minecraft.client.main.Main.main(Main.java:211) ~[minecraft-merged-7787b014d4-1.20.1-net.fabricmc.yarn.1_20_1.1.20.1+build.10-v2.jar:?]
	at net.fabricmc.loader.impl.game.minecraft.MinecraftGameProvider.launch(MinecraftGameProvider.java:480) ~[fabric-loader-0.16.13.jar:?]
	at net.fabricmc.loader.impl.launch.knot.Knot.launch(Knot.java:74) ~[fabric-loader-0.16.13.jar:?]
	at net.fabricmc.loader.impl.launch.knot.KnotClient.main(KnotClient.java:23) ~[fabric-loader-0.16.13.jar:?]
	at net.fabricmc.devlaunchinjector.Main.main(Main.java:86) ~[dev-launch-injector-0.2.1+build.8.jar:?]
Caused by: com.mojang.authlib.exceptions.MinecraftClientHttpException: Status: 401
	at com.mojang.authlib.minecraft.client.MinecraftClient.readInputStream(MinecraftClient.java:85) ~[authlib-4.0.43.jar:?]
	at com.mojang.authlib.minecraft.client.MinecraftClient.get(MinecraftClient.java:48) ~[authlib-4.0.43.jar:?]
	at com.mojang.authlib.yggdrasil.YggdrasilUserApiService.fetchProperties(YggdrasilUserApiService.java:129) ~[authlib-4.0.43.jar:?]
	... 9 more
[16:08:58] [Render thread/INFO] (net.minecraft.client.MinecraftClient) Setting user: Player2
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing KeyBindingAccessor from fabric-key-binding-api-v1.mixins.json into net.minecraft.client.option.KeyBinding
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-key-binding-api-v1.mixins.json:KeyBindingAccessor from mod fabric-key-binding-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) Renaming @Accessor method fabric_getCategoryMap()Ljava/util/Map; to fabric_getCategoryMap$fabric-key-binding-api-v1_$md$5da3e1$0 in fabric-key-binding-api-v1.mixins.json:KeyBindingAccessor from mod fabric-key-binding-api-v1
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing KeyBindingAccessor from fabric-events-interaction-v0.client.mixins.json into net.minecraft.client.option.KeyBinding
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-events-interaction-v0.client.mixins.json:KeyBindingAccessor from mod fabric-events-interaction-v0: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:58] [Render thread/DEBUG] (FabricLoader/Entrypoint) Iterating over entrypoint 'main'
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing MinecraftServerMixin from fabric-lifecycle-events-v1.mixins.json into net.minecraft.server.MinecraftServer
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:MinecraftServerMixin from mod fabric-lifecycle-events-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$endResourceReload$0(Ljava/lang/Void;Ljava/lang/Throwable;)Ljava/lang/Void; to md5da3e1$fabric-lifecycle-events-v1$lambda$endResourceReload$0$0 in fabric-lifecycle-events-v1.mixins.json:MinecraftServerMixin from mod fabric-lifecycle-events-v1
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing MinecraftServerMixin from fabric-gametest-api-v1.mixins.json into net.minecraft.server.MinecraftServer
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-gametest-api-v1.mixins.json:MinecraftServerMixin from mod fabric-gametest-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing MinecraftServerMixin from fabric-message-api-v1.mixins.json into net.minecraft.server.MinecraftServer
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-message-api-v1.mixins.json:MinecraftServerMixin from mod fabric-message-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$onGetChatDecorator$1(Lnet/minecraft/network/message/MessageDecorator;Lnet/minecraft/server/network/ServerPlayerEntity;Lnet/minecraft/text/Text;)Ljava/util/concurrent/CompletableFuture; to md5da3e1$fabric-message-api-v1$lambda$onGetChatDecorator$1$1 in fabric-message-api-v1.mixins.json:MinecraftServerMixin from mod fabric-message-api-v1
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$onGetChatDecorator$0(Lnet/minecraft/server/network/ServerPlayerEntity;Lnet/minecraft/text/Text;)Ljava/util/concurrent/CompletionStage; to md5da3e1$fabric-message-api-v1$lambda$onGetChatDecorator$0$2 in fabric-message-api-v1.mixins.json:MinecraftServerMixin from mod fabric-message-api-v1
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing MinecraftServerMixin from fabric-registry-sync-v0.mixins.json into net.minecraft.server.MinecraftServer
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.mixins.json:MinecraftServerMixin from mod fabric-registry-sync-v0: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing modification.MinecraftServerMixin from fabric-biome-api-v1.mixins.json into net.minecraft.server.MinecraftServer
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-biome-api-v1.mixins.json:modification.MinecraftServerMixin from mod fabric-biome-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing MinecraftServerMixin from fabric-resource-loader-v0.mixins.json into net.minecraft.server.MinecraftServer
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.mixins.json:MinecraftServerMixin from mod fabric-resource-loader-v0: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:MinecraftServerMixin from mod fabric-lifecycle-events-v1->@Inject::beforeSetupServer(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:MinecraftServerMixin from mod fabric-lifecycle-events-v1->@Inject::beforeSetupServer(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:MinecraftServerMixin from mod fabric-lifecycle-events-v1->@Inject::beforeSetupServer(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:MinecraftServerMixin from mod fabric-lifecycle-events-v1->@Inject::afterSetupServer(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:MinecraftServerMixin from mod fabric-lifecycle-events-v1->@Inject::afterSetupServer(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:MinecraftServerMixin from mod fabric-lifecycle-events-v1->@Inject::afterSetupServer(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:MinecraftServerMixin from mod fabric-lifecycle-events-v1->@Inject::beforeShutdownServer(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:MinecraftServerMixin from mod fabric-lifecycle-events-v1->@Inject::beforeShutdownServer(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:MinecraftServerMixin from mod fabric-lifecycle-events-v1->@Inject::beforeShutdownServer(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:MinecraftServerMixin from mod fabric-lifecycle-events-v1->@Inject::afterShutdownServer(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:MinecraftServerMixin from mod fabric-lifecycle-events-v1->@Inject::afterShutdownServer(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:MinecraftServerMixin from mod fabric-lifecycle-events-v1->@Inject::afterShutdownServer(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:MinecraftServerMixin from mod fabric-lifecycle-events-v1->@Inject::onStartTick(Ljava/util/function/BooleanSupplier;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:MinecraftServerMixin from mod fabric-lifecycle-events-v1->@Inject::onStartTick(Ljava/util/function/BooleanSupplier;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:MinecraftServerMixin from mod fabric-lifecycle-events-v1->@Inject::onStartTick(Ljava/util/function/BooleanSupplier;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:MinecraftServerMixin from mod fabric-lifecycle-events-v1->@Inject::onEndTick(Ljava/util/function/BooleanSupplier;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:MinecraftServerMixin from mod fabric-lifecycle-events-v1->@Inject::onEndTick(Ljava/util/function/BooleanSupplier;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:MinecraftServerMixin from mod fabric-lifecycle-events-v1->@Inject::onEndTick(Ljava/util/function/BooleanSupplier;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:MinecraftServerMixin from mod fabric-lifecycle-events-v1->@Inject::onUnloadWorldAtShutdown(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/Iterator;Lnet/minecraft/server/world/ServerWorld;)V doesn't use it's CallbackInfo
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:MinecraftServerMixin from mod fabric-lifecycle-events-v1->@Inject::onUnloadWorldAtShutdown(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/Iterator;Lnet/minecraft/server/world/ServerWorld;)V has 0 override(s) in child classes
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:MinecraftServerMixin from mod fabric-lifecycle-events-v1->@Inject::onUnloadWorldAtShutdown(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/Iterator;Lnet/minecraft/server/world/ServerWorld;)V won't be passed a CallbackInfo as a result
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:MinecraftServerMixin from mod fabric-lifecycle-events-v1->@Inject::startResourceReload(Ljava/util/Collection;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V doesn't use it's CallbackInfoReturnable
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:MinecraftServerMixin from mod fabric-lifecycle-events-v1->@Inject::startResourceReload(Ljava/util/Collection;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V has 0 override(s) in child classes
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:MinecraftServerMixin from mod fabric-lifecycle-events-v1->@Inject::startResourceReload(Ljava/util/Collection;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V won't be passed a CallbackInfoReturnable as a result
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.mixins.json:MinecraftServerMixin from mod fabric-lifecycle-events-v1->@Inject::endResourceReload(Ljava/util/Collection;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-gametest-api-v1.mixins.json:MinecraftServerMixin from mod fabric-gametest-api-v1->@Inject::tickWorlds(Ljava/util/function/BooleanSupplier;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-gametest-api-v1.mixins.json:MinecraftServerMixin from mod fabric-gametest-api-v1->@Inject::tickWorlds(Ljava/util/function/BooleanSupplier;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-gametest-api-v1.mixins.json:MinecraftServerMixin from mod fabric-gametest-api-v1->@Inject::tickWorlds(Ljava/util/function/BooleanSupplier;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-message-api-v1.mixins.json:MinecraftServerMixin from mod fabric-message-api-v1->@Inject::onGetChatDecorator(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.mixins.json:MinecraftServerMixin from mod fabric-registry-sync-v0->@Inject::beforeSetupServer(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.mixins.json:MinecraftServerMixin from mod fabric-registry-sync-v0->@Inject::beforeSetupServer(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.mixins.json:MinecraftServerMixin from mod fabric-registry-sync-v0->@Inject::beforeSetupServer(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-biome-api-v1.mixins.json:modification.MinecraftServerMixin from mod fabric-biome-api-v1->@Inject::finalizeWorldGen(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-biome-api-v1.mixins.json:modification.MinecraftServerMixin from mod fabric-biome-api-v1->@Inject::finalizeWorldGen(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-biome-api-v1.mixins.json:modification.MinecraftServerMixin from mod fabric-biome-api-v1->@Inject::finalizeWorldGen(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:58] [Render thread/INFO] (pokecobbleclaim) Initializing PokeCobbleClaim mod
[16:08:58] [Render thread/INFO] (pokecobbleclaim) Registering client-side network handlers
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing ClientPlayNetworkHandlerMixin from fabric-message-api-v1.client.mixins.json into net.minecraft.client.network.ClientPlayNetworkHandler
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing ClientPlayNetworkHandlerMixin from fabric-networking-api-v1.client.mixins.json into net.minecraft.client.network.ClientPlayNetworkHandler
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.client.mixins.json:ClientPlayNetworkHandlerMixin from mod fabric-networking-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing CommandMixin from pokecobbleclaim.mixins.json into net.minecraft.client.network.ClientPlayNetworkHandler
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$onGameMessage$0(Lnet/minecraft/client/MinecraftClient;)V to md5da3e1$pokecobbleclaim$lambda$onGameMessage$0$0 in pokecobbleclaim.mixins.json:CommandMixin from mod pokecobbleclaim
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing ClientPlayNetworkHandlerMixin from fabric-lifecycle-events-v1.client.mixins.json into net.minecraft.client.network.ClientPlayNetworkHandler
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:ClientPlayNetworkHandlerMixin from mod fabric-lifecycle-events-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing ClientPlayNetworkHandlerMixin from fabric-data-attachment-api-v1.client.mixins.json into net.minecraft.client.network.ClientPlayNetworkHandler
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing ClientPlayNetworkHandlerMixin from fabric-command-api-v2.client.mixins.json into net.minecraft.client.network.ClientPlayNetworkHandler
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-command-api-v2.client.mixins.json:ClientPlayNetworkHandlerMixin from mod fabric-command-api-v2: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-message-api-v1.client.mixins.json:ClientPlayNetworkHandlerMixin from mod fabric-message-api-v1->@Inject::fabric_allowSendChatMessage(Ljava/lang/String;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-message-api-v1.client.mixins.json:ClientPlayNetworkHandlerMixin from mod fabric-message-api-v1->@Inject::fabric_allowSendCommandMessage(Ljava/lang/String;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.client.mixins.json:ClientPlayNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::initAddon(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.client.mixins.json:ClientPlayNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::initAddon(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.client.mixins.json:ClientPlayNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::initAddon(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.client.mixins.json:ClientPlayNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::handleServerPlayReady(Lnet/minecraft/network/packet/s2c/play/GameJoinS2CPacket;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.client.mixins.json:ClientPlayNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::handleServerPlayReady(Lnet/minecraft/network/packet/s2c/play/GameJoinS2CPacket;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.client.mixins.json:ClientPlayNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::handleServerPlayReady(Lnet/minecraft/network/packet/s2c/play/GameJoinS2CPacket;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.client.mixins.json:ClientPlayNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::handleCustomPayload(Lnet/minecraft/network/packet/s2c/play/CustomPayloadS2CPacket;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.client.mixins.json:ClientPlayNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::handleDisconnection(Lnet/minecraft/text/Text;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.client.mixins.json:ClientPlayNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::handleDisconnection(Lnet/minecraft/text/Text;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.client.mixins.json:ClientPlayNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::handleDisconnection(Lnet/minecraft/text/Text;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) pokecobbleclaim.mixins.json:CommandMixin from mod pokecobbleclaim->@Inject::onGameMessage(Lnet/minecraft/network/packet/s2c/play/GameMessageS2CPacket;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) pokecobbleclaim.mixins.json:CommandMixin from mod pokecobbleclaim->@Inject::onGameMessage(Lnet/minecraft/network/packet/s2c/play/GameMessageS2CPacket;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) pokecobbleclaim.mixins.json:CommandMixin from mod pokecobbleclaim->@Inject::onGameMessage(Lnet/minecraft/network/packet/s2c/play/GameMessageS2CPacket;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:ClientPlayNetworkHandlerMixin from mod fabric-lifecycle-events-v1->@Inject::onPlayerRespawn(Lnet/minecraft/network/packet/s2c/play/PlayerRespawnS2CPacket;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:ClientPlayNetworkHandlerMixin from mod fabric-lifecycle-events-v1->@Inject::onPlayerRespawn(Lnet/minecraft/network/packet/s2c/play/PlayerRespawnS2CPacket;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:ClientPlayNetworkHandlerMixin from mod fabric-lifecycle-events-v1->@Inject::onPlayerRespawn(Lnet/minecraft/network/packet/s2c/play/PlayerRespawnS2CPacket;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:ClientPlayNetworkHandlerMixin from mod fabric-lifecycle-events-v1->@Inject::onGameJoin(Lnet/minecraft/network/packet/s2c/play/GameJoinS2CPacket;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:ClientPlayNetworkHandlerMixin from mod fabric-lifecycle-events-v1->@Inject::onGameJoin(Lnet/minecraft/network/packet/s2c/play/GameJoinS2CPacket;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:ClientPlayNetworkHandlerMixin from mod fabric-lifecycle-events-v1->@Inject::onGameJoin(Lnet/minecraft/network/packet/s2c/play/GameJoinS2CPacket;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:ClientPlayNetworkHandlerMixin from mod fabric-lifecycle-events-v1->@Inject::onClearWorld(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:ClientPlayNetworkHandlerMixin from mod fabric-lifecycle-events-v1->@Inject::onClearWorld(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:ClientPlayNetworkHandlerMixin from mod fabric-lifecycle-events-v1->@Inject::onClearWorld(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:ClientPlayNetworkHandlerMixin from mod fabric-lifecycle-events-v1->@Inject::hookOnSynchronizeTags(Lnet/minecraft/network/packet/s2c/play/SynchronizeTagsS2CPacket;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:ClientPlayNetworkHandlerMixin from mod fabric-lifecycle-events-v1->@Inject::hookOnSynchronizeTags(Lnet/minecraft/network/packet/s2c/play/SynchronizeTagsS2CPacket;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-lifecycle-events-v1.client.mixins.json:ClientPlayNetworkHandlerMixin from mod fabric-lifecycle-events-v1->@Inject::hookOnSynchronizeTags(Lnet/minecraft/network/packet/s2c/play/SynchronizeTagsS2CPacket;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-command-api-v2.client.mixins.json:ClientPlayNetworkHandlerMixin from mod fabric-command-api-v2->@Inject::onGameJoin(Lnet/minecraft/network/packet/s2c/play/GameJoinS2CPacket;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-command-api-v2.client.mixins.json:ClientPlayNetworkHandlerMixin from mod fabric-command-api-v2->@Inject::onGameJoin(Lnet/minecraft/network/packet/s2c/play/GameJoinS2CPacket;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-command-api-v2.client.mixins.json:ClientPlayNetworkHandlerMixin from mod fabric-command-api-v2->@Inject::onGameJoin(Lnet/minecraft/network/packet/s2c/play/GameJoinS2CPacket;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-command-api-v2.client.mixins.json:ClientPlayNetworkHandlerMixin from mod fabric-command-api-v2->@Inject::onOnCommandTree(Lnet/minecraft/network/packet/s2c/play/CommandTreeS2CPacket;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-command-api-v2.client.mixins.json:ClientPlayNetworkHandlerMixin from mod fabric-command-api-v2->@Inject::onOnCommandTree(Lnet/minecraft/network/packet/s2c/play/CommandTreeS2CPacket;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-command-api-v2.client.mixins.json:ClientPlayNetworkHandlerMixin from mod fabric-command-api-v2->@Inject::onOnCommandTree(Lnet/minecraft/network/packet/s2c/play/CommandTreeS2CPacket;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-command-api-v2.client.mixins.json:ClientPlayNetworkHandlerMixin from mod fabric-command-api-v2->@Inject::onSendCommand(Ljava/lang/String;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-command-api-v2.client.mixins.json:ClientPlayNetworkHandlerMixin from mod fabric-command-api-v2->@Inject::onSendCommand(Ljava/lang/String;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[16:08:58] [Render thread/INFO] (pokecobbleclaim) Economy client-side network handlers registered
[16:08:58] [Render thread/INFO] (pokecobbleclaim) Registered client town jobs network handlers
[16:08:58] [Render thread/INFO] (pokecobbleclaim) Registered ClientGlobalChunkSyncHandler network handlers
[16:08:58] [Render thread/INFO] (pokecobbleclaim) Enhanced Image Transfer Manager initialized (client-side)
[16:08:58] [Render thread/INFO] (pokecobbleclaim) Registering ConfigSynchronizer client-side packet handlers
[16:08:58] [Render thread/INFO] (pokecobbleclaim) Registered CONFIG_SYNC handler: pokecobbleclaim:config_sync
[16:08:58] [Render thread/INFO] (pokecobbleclaim) Registered USER_PREFERENCES_SYNC handler: pokecobbleclaim:user_preferences_sync
[16:08:58] [Render thread/INFO] (pokecobbleclaim) Registering server-side network handlers
[16:08:58] [Render thread/INFO] (pokecobbleclaim) Registered town jobs network handlers
[16:08:58] [Render thread/INFO] (pokecobbleclaim) Registered town request network handlers
[16:08:58] [Render thread/INFO] (pokecobbleclaim) Registered GlobalChunkSyncHandler network handlers
[16:08:58] [Render thread/INFO] (pokecobbleclaim) Enhanced Image Transfer Manager initialized (server-side)
[16:08:58] [Render thread/INFO] (pokecobbleclaim) Registering ConfigSynchronizer server-side packet handlers
[16:08:58] [Render thread/INFO] (pokecobbleclaim) Registered CONFIG_REQUEST handler: pokecobbleclaim:config_request
[16:08:58] [Render thread/INFO] (pokecobbleclaim) Registered CONFIG_UPDATE handler: pokecobbleclaim:config_update
[16:08:58] [Render thread/DEBUG] (pokecobbleclaim) Registered server-side food price network handlers
[16:08:58] [Render thread/DEBUG] (pokecobbleclaim) Registered server-side food price timer network handlers
[16:08:58] [Render thread/INFO] (pokecobbleclaim) Reset all town and player data versions
[16:08:58] [Render thread/INFO] (pokecobbleclaim) No individual town files found
[16:08:58] [Render thread/DEBUG] (pokecobbleclaim) TownManager.getAllTowns() returning 0 towns
[16:08:58] [Render thread/INFO] (pokecobbleclaim) Town data loading completed. Final town count in TownManager: 0
[16:08:58] [Render thread/INFO] (pokecobbleclaim) Simple chunk protection handler initialized
[16:08:58] [Render thread/INFO] (pokecobbleclaim) Simple chunk protection system initialized successfully - complex systems disabled
[16:08:58] [Render thread/INFO] (pokecobbleclaim) === Testing Simple Permission System ===
[16:08:58] [Render thread/INFO] (pokecobbleclaim) ✓ Test 1: Unclaimed chunk logic verified (allows all actions)
[16:08:58] [Render thread/INFO] (pokecobbleclaim) ✓ Test 2: Permission constants are correct
[16:08:58] [Render thread/INFO] (pokecobbleclaim) ✓ Test 3: Permission name mapping works correctly
[16:08:58] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Checking permission for rank MEMBER (Resident), permissionIndex 0
[16:08:58] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Retrieved permissions array for rank MEMBER: [true, false, false, false, false, false, false, false]
[16:08:58] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Town member permission result = true
[16:08:58] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Checking permission for rank VISITOR (Citizen), permissionIndex 0
[16:08:58] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Retrieved permissions array for rank VISITOR: [false, false, false, false, false, false, false, false]
[16:08:58] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Town member permission result = false
[16:08:58] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Checking permission for rank OWNER (Mayor), permissionIndex 0
[16:08:58] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Retrieved permissions array for rank OWNER: [true, true, true, true, true, true, true, true]
[16:08:58] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Town member permission result = true
[16:08:58] [Render thread/INFO] (pokecobbleclaim) ✓ Test 4: Individual rank permissions work correctly (non-hierarchical)
[16:08:58] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Checking permission for rank null (null), permissionIndex 1
[16:08:58] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Non-member permission result = true
[16:08:58] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Checking permission for rank null (null), permissionIndex 0
[16:08:58] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Non-member permission result = false
[16:08:58] [Render thread/INFO] (pokecobbleclaim) ✓ Test 5: Non-member permissions work correctly
[16:08:58] [Render thread/INFO] (pokecobbleclaim) === All Simple Permission System Tests PASSED ===
[16:08:58] [Render thread/INFO] (pokecobbleclaim) === Testing Individual Rank Permissions (Non-Hierarchical) ===
[16:08:58] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Checking permission for rank MEMBER (Resident), permissionIndex 2
[16:08:58] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Retrieved permissions array for rank MEMBER: [false, false, true, false, false, false, false, false]
[16:08:58] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Town member permission result = true
[16:08:58] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Checking permission for rank VISITOR (Citizen), permissionIndex 2
[16:08:58] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Retrieved permissions array for rank VISITOR: [false, false, false, false, false, false, false, false]
[16:08:58] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Town member permission result = false
[16:08:58] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Checking permission for rank MODERATOR (Council), permissionIndex 2
[16:08:58] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Retrieved permissions array for rank MODERATOR: [false, false, false, false, false, false, false, false]
[16:08:58] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Town member permission result = false
[16:08:58] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Checking permission for rank OWNER (Mayor), permissionIndex 2
[16:08:58] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Retrieved permissions array for rank OWNER: [true, true, true, true, true, true, true, true]
[16:08:58] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Town member permission result = true
[16:08:58] [Render thread/INFO] (pokecobbleclaim) ✓ Individual rank permissions test PASSED - only specified rank gets permission (plus admin ranks)
[16:08:58] [Render thread/INFO] (pokecobbleclaim) 🎉 ALL PERMISSION SYSTEM TESTS PASSED! The new simple system is working correctly.
[16:08:58] [Render thread/INFO] (pokecobbleclaim) Claim tag sync handlers initialized
[16:08:58] [Render thread/INFO] (pokecobbleclaim) ChunkClaimPacketHandler: Starting initialization...
[16:08:58] [Render thread/INFO] (pokecobbleclaim) ChunkClaimPacketHandler: Registering server-side handler for CHUNK_CLAIM_REQUEST: pokecobbleclaim:chunk_claim_request
[16:08:58] [Render thread/INFO] (pokecobbleclaim) ChunkClaimPacketHandler: Successfully registered CHUNK_CLAIM_REQUEST handler
[16:08:58] [Render thread/INFO] (pokecobbleclaim) Chunk claim packet handlers initialized
[16:08:58] [Render thread/INFO] (pokecobbleclaim) Chunk claim sync handlers initialized
[16:08:58] [Render thread/INFO] (pokecobbleclaim) Claim tool selection sync handlers initialized
[16:08:58] [Render thread/INFO] (pokecobbleclaim) PokeCobbleClaim mod initialized successfully
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing ServerLoginNetworkHandlerMixin from fabric-networking-api-v1.mixins.json into net.minecraft.server.network.ServerLoginNetworkHandler
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:ServerLoginNetworkHandlerMixin from mod fabric-networking-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing accessor.ServerLoginNetworkHandlerAccessor from fabric-networking-api-v1.mixins.json into net.minecraft.server.network.ServerLoginNetworkHandler
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:accessor.ServerLoginNetworkHandlerAccessor from mod fabric-networking-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:ServerLoginNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::initAddon(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:ServerLoginNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::initAddon(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:ServerLoginNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::initAddon(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:ServerLoginNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::handleCustomPayloadReceivedAsync(Lnet/minecraft/network/packet/c2s/login/LoginQueryResponseC2SPacket;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:ServerLoginNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::handleDisconnection(Lnet/minecraft/text/Text;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:ServerLoginNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::handleDisconnection(Lnet/minecraft/text/Text;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:ServerLoginNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::handleDisconnection(Lnet/minecraft/text/Text;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:ServerLoginNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::handlePlayTransitionNormal(Lnet/minecraft/server/network/ServerPlayerEntity;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:ServerLoginNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::handlePlayTransitionNormal(Lnet/minecraft/server/network/ServerPlayerEntity;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:ServerLoginNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::handlePlayTransitionNormal(Lnet/minecraft/server/network/ServerPlayerEntity;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing LootTableBuilderMixin from fabric-loot-api-v2.mixins.json into net.minecraft.loot.LootTable$Builder
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-loot-api-v2.mixins.json:LootTableBuilderMixin from mod fabric-loot-api-v2: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_8 supports class version 52)
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing LootManagerMixin from fabric-resource-conditions-api-v1.mixins.json into net.minecraft.loot.LootManager
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-conditions-api-v1.mixins.json:LootManagerMixin from mod fabric-resource-conditions-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing KeyedResourceReloadListenerMixin from fabric-resource-loader-v0.mixins.json into net.minecraft.loot.LootManager
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.mixins.json:KeyedResourceReloadListenerMixin from mod fabric-resource-loader-v0: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing LootManagerMixin from fabric-loot-api-v2.mixins.json into net.minecraft.loot.LootManager
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-loot-api-v2.mixins.json:LootManagerMixin from mod fabric-loot-api-v2: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_8 supports class version 52)
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$applyLootTableEvents$1(Lcom/google/common/collect/ImmutableMap$Builder;Lnet/minecraft/resource/ResourceManager;Lnet/minecraft/loot/LootManager;Lnet/minecraft/loot/LootDataKey;Ljava/lang/Object;)V to md5da3e1$fabric-loot-api-v2$lambda$applyLootTableEvents$1$0 in fabric-loot-api-v2.mixins.json:LootManagerMixin from mod fabric-loot-api-v2
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$reload$0(Lnet/minecraft/resource/ResourceManager;Lnet/minecraft/loot/LootManager;)V to md5da3e1$fabric-loot-api-v2$lambda$reload$0$1 in fabric-loot-api-v2.mixins.json:LootManagerMixin from mod fabric-loot-api-v2
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-conditions-api-v1.mixins.json:LootManagerMixin from mod fabric-resource-conditions-api-v1->@Inject::load(Lnet/minecraft/loot/LootDataType;Lnet/minecraft/resource/ResourceManager;Ljava/util/concurrent/Executor;Ljava/util/Map;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;Ljava/util/Map;)V doesn't use it's CallbackInfoReturnable
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-conditions-api-v1.mixins.json:LootManagerMixin from mod fabric-resource-conditions-api-v1->@Inject::runAsync(Lnet/minecraft/resource/ResourceManager;Lnet/minecraft/loot/LootDataType;Ljava/util/Map;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-conditions-api-v1.mixins.json:LootManagerMixin from mod fabric-resource-conditions-api-v1->@Inject::applyResourceConditions(Lnet/minecraft/loot/LootDataType;Ljava/util/Map;Lnet/minecraft/util/Identifier;Lcom/google/gson/JsonElement;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-conditions-api-v1.mixins.json:LootManagerMixin from mod fabric-resource-conditions-api-v1->@Inject::runAsyncEnd(Lnet/minecraft/resource/ResourceManager;Lnet/minecraft/loot/LootDataType;Ljava/util/Map;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-loot-api-v2.mixins.json:LootManagerMixin from mod fabric-loot-api-v2->@Inject::reload(Lnet/minecraft/resource/ResourceReloader$Synchronizer;Lnet/minecraft/resource/ResourceManager;Lnet/minecraft/util/profiler/Profiler;Lnet/minecraft/util/profiler/Profiler;Ljava/util/concurrent/Executor;Ljava/util/concurrent/Executor;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing LootTableMixin from fabric-loot-tables-v1.mixins.json into net.minecraft.loot.LootTable
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-loot-tables-v1.mixins.json:LootTableMixin from mod fabric-loot-tables-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing LootTableAccessor from fabric-loot-api-v2.mixins.json into net.minecraft.loot.LootTable
[16:08:58] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-loot-api-v2.mixins.json:LootTableAccessor from mod fabric-loot-api-v2: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_8 supports class version 52)
[16:08:58] [Render thread/DEBUG] (FabricLoader/Entrypoint) Iterating over entrypoint 'client'
[16:08:58] [Render thread/INFO] (pokecobbleclaim) Initializing PokeCobbleClaim client
[16:08:58] [Render thread/INFO] (pokecobbleclaim) Registering town keybinding
[16:08:58] [Render thread/INFO] (pokecobbleclaim) Registering chunk boundary renderer
[16:08:59] [Render thread/DEBUG] (pokecobbleclaim) Started periodic cache cleanup
[16:08:59] [Render thread/DEBUG] (pokecobbleclaim) Set up claim tool data sync event handlers
[16:08:59] [Render thread/DEBUG] (pokecobbleclaim) Set up UI data refresh handlers
[16:08:59] [Render thread/DEBUG] (pokecobbleclaim) Set up default auto-refresh intervals for screen types
[16:08:59] [Render thread/DEBUG] (pokecobbleclaim) Started periodic refresh check
[16:08:59] [Render thread/INFO] (pokecobbleclaim) Loaded user preferences from: pokecobbleclaim-user-preferences.json
[16:08:59] [Render thread/INFO] (pokecobbleclaim) Client Permission Handler v2 temporarily disabled for debugging
[16:08:59] [Render thread/INFO] (pokecobbleclaim) Initialized client-side data managers for synchronization
[16:08:59] [Render thread/INFO] (pokecobbleclaim) Registering sounds
[16:08:59] [Render thread/INFO] (pokecobbleclaim) Successfully registered sound: pokecobbleclaim:notification.invite
[16:08:59] [Render thread/INFO] (pokecobbleclaim) Successfully registered sound: pokecobbleclaim:ui.button.click
[16:08:59] [Render thread/INFO] (pokecobbleclaim) Registered sounds on client side
[16:08:59] [Render thread/INFO] (pokecobbleclaim) Registering client-side network handlers
[16:08:59] [Render thread/INFO] (pokecobbleclaim) Economy client-side network handlers registered
[16:08:59] [Render thread/INFO] (pokecobbleclaim) Registered ClientGlobalChunkSyncHandler network handlers
[16:08:59] [Render thread/INFO] (pokecobbleclaim) Enhanced Image Transfer Manager initialized (client-side)
[16:08:59] [Render thread/INFO] (pokecobbleclaim) Registering ConfigSynchronizer client-side packet handlers
[16:08:59] [Render thread/INFO] (pokecobbleclaim) Registered CONFIG_SYNC handler: pokecobbleclaim:config_sync
[16:08:59] [Render thread/INFO] (pokecobbleclaim) Registered USER_PREFERENCES_SYNC handler: pokecobbleclaim:user_preferences_sync
[16:08:59] [Render thread/INFO] (pokecobbleclaim) Registering server-side network handlers
[16:08:59] [Render thread/INFO] (pokecobbleclaim) Registered town jobs network handlers
[16:08:59] [Render thread/INFO] (pokecobbleclaim) Registered town request network handlers
[16:08:59] [Render thread/INFO] (pokecobbleclaim) Registered GlobalChunkSyncHandler network handlers
[16:08:59] [Render thread/INFO] (pokecobbleclaim) Enhanced Image Transfer Manager initialized (server-side)
[16:08:59] [Render thread/INFO] (pokecobbleclaim) Registering ConfigSynchronizer server-side packet handlers
[16:08:59] [Render thread/INFO] (pokecobbleclaim) Registered CONFIG_REQUEST handler: pokecobbleclaim:config_request
[16:08:59] [Render thread/INFO] (pokecobbleclaim) Registered CONFIG_UPDATE handler: pokecobbleclaim:config_update
[16:08:59] [Render thread/DEBUG] (pokecobbleclaim) Registered client-side food price network handlers
[16:08:59] [Render thread/DEBUG] (pokecobbleclaim) Registered client-side food price timer network handlers
[16:08:59] [Render thread/INFO] (pokecobbleclaim) Initializing default client products
[16:08:59] [Render thread/INFO] (pokecobbleclaim) Economy client-side network handlers registered
[16:08:59] [Render thread/INFO] (pokecobbleclaim) Registered client-side network handlers
[16:08:59] [Render thread/INFO] (pokecobbleclaim) Initializing phone feature
[16:08:59] [Render thread/INFO] (pokecobbleclaim) Initializing app position manager
[16:08:59] [Render thread/INFO] (pokecobbleclaim) Initializing app position manager
[16:08:59] [Render thread/INFO] (pokecobbleclaim) Setting up default app positions
[16:08:59] [Render thread/INFO] (pokecobbleclaim) Initialized 24 default app positions
[16:08:59] [Render thread/INFO] (pokecobbleclaim) Initializing app registry
[16:08:59] [Render thread/INFO] (pokecobbleclaim) Initializing app registry
[16:08:59] [Render thread/INFO] (pokecobbleclaim) Registered 6 apps
[16:08:59] [Render thread/INFO] (pokecobbleclaim) Initializing phone texture manager
[16:08:59] [Render thread/INFO] (pokecobbleclaim) Initializing and registering phone notification overlay
[16:08:59] [Render thread/INFO] (pokecobbleclaim) Registering phone notification overlay renderer
[16:08:59] [Render thread/INFO] (pokecobbleclaim) Registering phone notification renderer
[16:08:59] [Render thread/INFO] (pokecobbleclaim) Registered phone notification renderer
[16:08:59] [Render thread/INFO] (pokecobbleclaim) Registering phone keybinding
[16:08:59] [Render thread/INFO] (pokecobbleclaim) Registering phone keybinding
[16:08:59] [Render thread/INFO] (pokecobbleclaim) Initialized phone feature
[16:08:59] [Render thread/INFO] (pokecobbleclaim) Registering Shape Visualizer Tool
[16:08:59] [Render thread/INFO] (pokecobbleclaim) Registered shape visualizer tool
[16:08:59] [Render thread/INFO] (pokecobbleclaim) PokeCobbleClaim client initialized successfully
[16:08:59] [Render thread/INFO] (net.fabricmc.fabric.impl.client.indigo.Indigo) [Indigo] Registering Indigo renderer!
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing GameRendererMixin from fabric-screen-api-v1.mixins.json into net.minecraft.client.render.GameRenderer
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:GameRendererMixin from mod fabric-screen-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing shader.GameRendererMixin from fabric-rendering-v1.mixins.json into net.minecraft.client.render.GameRenderer
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$registerShaders$0(Lnet/minecraft/resource/ResourceFactory;Ljava/util/List;Lnet/minecraft/util/Identifier;Lnet/minecraft/client/render/VertexFormat;Ljava/util/function/Consumer;)V to md5da3e1$fabric-rendering-v1$lambda$registerShaders$0$0 in fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:GameRendererMixin from mod fabric-screen-api-v1->@Inject::onBeforeRenderScreen(FJZLorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;IILnet/minecraft/client/util/math/MatrixStack;Lnet/minecraft/client/gui/DrawContext;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:GameRendererMixin from mod fabric-screen-api-v1->@Inject::onBeforeRenderScreen(FJZLorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;IILnet/minecraft/client/util/math/MatrixStack;Lnet/minecraft/client/gui/DrawContext;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:GameRendererMixin from mod fabric-screen-api-v1->@Inject::onBeforeRenderScreen(FJZLorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;IILnet/minecraft/client/util/math/MatrixStack;Lnet/minecraft/client/gui/DrawContext;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:GameRendererMixin from mod fabric-screen-api-v1->@Inject::onAfterRenderScreen(FJZLorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;IILnet/minecraft/client/util/math/MatrixStack;Lnet/minecraft/client/gui/DrawContext;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:GameRendererMixin from mod fabric-screen-api-v1->@Inject::onAfterRenderScreen(FJZLorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;IILnet/minecraft/client/util/math/MatrixStack;Lnet/minecraft/client/gui/DrawContext;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:GameRendererMixin from mod fabric-screen-api-v1->@Inject::onAfterRenderScreen(FJZLorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;IILnet/minecraft/client/util/math/MatrixStack;Lnet/minecraft/client/gui/DrawContext;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.GameRendererMixin from mod fabric-rendering-v1->@Inject::registerShaders(Lnet/minecraft/resource/ResourceFactory;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Ljava/util/List;Ljava/util/List;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing shader.ShaderProgramMixin from fabric-rendering-v1.mixins.json into net.minecraft.client.gl.ShaderProgram
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.ShaderProgramMixin from mod fabric-rendering-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing client.SpriteAtlasTextureMixin from fabric-renderer-api-v1.mixins.json into net.minecraft.client.texture.SpriteAtlasTexture
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-renderer-api-v1.mixins.json:client.SpriteAtlasTextureMixin from mod fabric-renderer-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-renderer-api-v1.mixins.json:client.SpriteAtlasTextureMixin from mod fabric-renderer-api-v1->@Inject::uploadHook(Lnet/minecraft/client/texture/SpriteLoader$StitchResult;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-renderer-api-v1.mixins.json:client.SpriteAtlasTextureMixin from mod fabric-renderer-api-v1->@Inject::uploadHook(Lnet/minecraft/client/texture/SpriteLoader$StitchResult;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-renderer-api-v1.mixins.json:client.SpriteAtlasTextureMixin from mod fabric-renderer-api-v1->@Inject::uploadHook(Lnet/minecraft/client/texture/SpriteLoader$StitchResult;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing ItemRendererMixin from fabric-renderer-indigo.mixins.json into net.minecraft.client.render.item.ItemRenderer
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-renderer-indigo.mixins.json:ItemRendererMixin from mod fabric-renderer-indigo: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$new$0()Lnet/fabricmc/fabric/impl/client/indigo/renderer/render/ItemRenderContext; to md5da3e1$fabric-renderer-indigo$lambda$new$0$0 in fabric-renderer-indigo.mixins.json:ItemRendererMixin from mod fabric-renderer-indigo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing KeyedResourceReloadListenerClientMixin from fabric-resource-loader-v0.client.mixins.json into net.minecraft.client.render.item.ItemRenderer
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.client.mixins.json:KeyedResourceReloadListenerClientMixin from mod fabric-resource-loader-v0: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-renderer-indigo.mixins.json:ItemRendererMixin from mod fabric-renderer-indigo->@Inject::hook_renderItem(Lnet/minecraft/item/ItemStack;Lnet/minecraft/client/render/model/json/ModelTransformationMode;ZLnet/minecraft/client/util/math/MatrixStack;Lnet/minecraft/client/render/VertexConsumerProvider;IILnet/minecraft/client/render/model/BakedModel;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing accessor.ClientLoginNetworkHandlerAccessor from fabric-networking-api-v1.client.mixins.json into net.minecraft.client.network.ClientLoginNetworkHandler
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.client.mixins.json:accessor.ClientLoginNetworkHandlerAccessor from mod fabric-networking-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing ClientLoginNetworkHandlerMixin from fabric-networking-api-v1.client.mixins.json into net.minecraft.client.network.ClientLoginNetworkHandler
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.client.mixins.json:ClientLoginNetworkHandlerMixin from mod fabric-networking-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.client.mixins.json:ClientLoginNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::initAddon(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.client.mixins.json:ClientLoginNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::initAddon(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.client.mixins.json:ClientLoginNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::initAddon(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.client.mixins.json:ClientLoginNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::handleQueryRequest(Lnet/minecraft/network/packet/s2c/login/LoginQueryRequestS2CPacket;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.client.mixins.json:ClientLoginNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::invokeLoginDisconnectEvent(Lnet/minecraft/text/Text;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.client.mixins.json:ClientLoginNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::invokeLoginDisconnectEvent(Lnet/minecraft/text/Text;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.client.mixins.json:ClientLoginNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::invokeLoginDisconnectEvent(Lnet/minecraft/text/Text;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.client.mixins.json:ClientLoginNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::handlePlayTransition(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.client.mixins.json:ClientLoginNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::handlePlayTransition(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.client.mixins.json:ClientLoginNetworkHandlerMixin from mod fabric-networking-api-v1->@Inject::handlePlayTransition(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing ClientConnectionMixin from fabric-networking-api-v1.mixins.json into net.minecraft.network.ClientConnection
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:ClientConnectionMixin from mod fabric-networking-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:ClientConnectionMixin from mod fabric-networking-api-v1->@Inject::initAddedFields(Lnet/minecraft/network/NetworkSide;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:ClientConnectionMixin from mod fabric-networking-api-v1->@Inject::initAddedFields(Lnet/minecraft/network/NetworkSide;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:ClientConnectionMixin from mod fabric-networking-api-v1->@Inject::initAddedFields(Lnet/minecraft/network/NetworkSide;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:ClientConnectionMixin from mod fabric-networking-api-v1->@Inject::checkPacket(Lnet/minecraft/network/packet/Packet;Lnet/minecraft/network/PacketCallbacks;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:ClientConnectionMixin from mod fabric-networking-api-v1->@Inject::checkPacket(Lnet/minecraft/network/packet/Packet;Lnet/minecraft/network/PacketCallbacks;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:ClientConnectionMixin from mod fabric-networking-api-v1->@Inject::checkPacket(Lnet/minecraft/network/packet/Packet;Lnet/minecraft/network/PacketCallbacks;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:ClientConnectionMixin from mod fabric-networking-api-v1->@Inject::checkPacket(Lnet/minecraft/network/packet/Packet;Lnet/minecraft/network/PacketCallbacks;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:ClientConnectionMixin from mod fabric-networking-api-v1->@Inject::checkPacket(Lnet/minecraft/network/packet/Packet;Lnet/minecraft/network/PacketCallbacks;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:ClientConnectionMixin from mod fabric-networking-api-v1->@Inject::checkPacket(Lnet/minecraft/network/packet/Packet;Lnet/minecraft/network/PacketCallbacks;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:ClientConnectionMixin from mod fabric-networking-api-v1->@Inject::handleDisconnect(Lio/netty/channel/ChannelHandlerContext;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:ClientConnectionMixin from mod fabric-networking-api-v1->@Inject::handleDisconnect(Lio/netty/channel/ChannelHandlerContext;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:ClientConnectionMixin from mod fabric-networking-api-v1->@Inject::handleDisconnect(Lio/netty/channel/ChannelHandlerContext;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-networking-api-v1.mixins.json:ClientConnectionMixin from mod fabric-networking-api-v1->@Inject::sendInternal(Lnet/minecraft/network/packet/Packet;Lnet/minecraft/network/PacketCallbacks;Lnet/minecraft/network/NetworkState;Lnet/minecraft/network/NetworkState;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;Lio/netty/channel/ChannelFuture;)V does use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing GameOptionsMixin from fabric-resource-loader-v0.client.mixins.json into net.minecraft.client.option.GameOptions
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.client.mixins.json:GameOptionsMixin from mod fabric-resource-loader-v0: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing GameOptionsMixin from fabric-key-binding-api-v1.mixins.json into net.minecraft.client.option.GameOptions
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-key-binding-api-v1.mixins.json:GameOptionsMixin from mod fabric-key-binding-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.client.mixins.json:GameOptionsMixin from mod fabric-resource-loader-v0->@Inject::onLoad(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.client.mixins.json:GameOptionsMixin from mod fabric-resource-loader-v0->@Inject::onLoad(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.client.mixins.json:GameOptionsMixin from mod fabric-resource-loader-v0->@Inject::onLoad(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.client.mixins.json:GameOptionsMixin from mod fabric-resource-loader-v0->@Inject::onLoad(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.client.mixins.json:GameOptionsMixin from mod fabric-resource-loader-v0->@Inject::onLoad(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.client.mixins.json:GameOptionsMixin from mod fabric-resource-loader-v0->@Inject::onLoad(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-key-binding-api-v1.mixins.json:GameOptionsMixin from mod fabric-key-binding-api-v1->@Inject::loadHook(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-key-binding-api-v1.mixins.json:GameOptionsMixin from mod fabric-key-binding-api-v1->@Inject::loadHook(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-key-binding-api-v1.mixins.json:GameOptionsMixin from mod fabric-key-binding-api-v1->@Inject::loadHook(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing SoundInstanceMixin from fabric-sound-api-v1.mixins.json into net.minecraft.client.sound.SoundInstance
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing ResourcePackProfileMixin from fabric-resource-loader-v0.mixins.json into net.minecraft.resource.ResourcePackProfile
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.mixins.json:ResourcePackProfileMixin from mod fabric-resource-loader-v0: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:08:59] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.mixins.json:ResourcePackProfileMixin from mod fabric-resource-loader-v0->@Inject::onCreateResourcePack(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[16:08:59] [Render thread/INFO] (net.minecraft.client.MinecraftClient) Backend library: LWJGL version 3.3.1 SNAPSHOT
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing MouseScrollMixin from pokecobbleclaim.mixins.json into net.minecraft.client.Mouse
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing ClaimToolMouseMixin from pokecobbleclaim.mixins.json into net.minecraft.client.Mouse
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing MouseMixin from fabric-screen-api-v1.mixins.json into net.minecraft.client.Mouse
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:MouseMixin from mod fabric-screen-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) pokecobbleclaim.mixins.json:MouseScrollMixin from mod pokecobbleclaim->@Inject::onMouseScroll(JDDLorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) pokecobbleclaim.mixins.json:ClaimToolMouseMixin from mod pokecobbleclaim->@Inject::onMouseButton(JIIILorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:MouseMixin from mod fabric-screen-api-v1->@Inject::beforeMouseClickedEvent([ZLnet/minecraft/client/gui/screen/Screen;DDILorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:MouseMixin from mod fabric-screen-api-v1->@Inject::afterMouseClickedEvent([ZLnet/minecraft/client/gui/screen/Screen;DDILorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:MouseMixin from mod fabric-screen-api-v1->@Inject::beforeMouseReleasedEvent([ZLnet/minecraft/client/gui/screen/Screen;DDILorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:MouseMixin from mod fabric-screen-api-v1->@Inject::afterMouseReleasedEvent([ZLnet/minecraft/client/gui/screen/Screen;DDILorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:MouseMixin from mod fabric-screen-api-v1->@Inject::beforeMouseScrollEvent(JDDLorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;DDD)V does use it's CallbackInfo
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:MouseMixin from mod fabric-screen-api-v1->@Inject::afterMouseScrollEvent(JDDLorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;DDD)V doesn't use it's CallbackInfo
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:MouseMixin from mod fabric-screen-api-v1->@Inject::afterMouseScrollEvent(JDDLorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;DDD)V has 0 override(s) in child classes
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:MouseMixin from mod fabric-screen-api-v1->@Inject::afterMouseScrollEvent(JDDLorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;DDD)V won't be passed a CallbackInfo as a result
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing KeyboardMixin from pokecobbleclaim.mixins.json into net.minecraft.client.Keyboard
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing KeyboardMixin from fabric-screen-api-v1.mixins.json into net.minecraft.client.Keyboard
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:KeyboardMixin from mod fabric-screen-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) pokecobbleclaim.mixins.json:KeyboardMixin from mod pokecobbleclaim->@Inject::onKey(JIIIILorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) pokecobbleclaim.mixins.json:KeyboardMixin from mod pokecobbleclaim->@Inject::onProcessF3(ILorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:KeyboardMixin from mod fabric-screen-api-v1->@Inject::beforeKeyPressedEvent(ILnet/minecraft/client/gui/screen/Screen;[ZIIILorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:KeyboardMixin from mod fabric-screen-api-v1->@Inject::afterKeyPressedEvent(ILnet/minecraft/client/gui/screen/Screen;[ZIIILorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:KeyboardMixin from mod fabric-screen-api-v1->@Inject::beforeKeyReleasedEvent(ILnet/minecraft/client/gui/screen/Screen;[ZIIILorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-screen-api-v1.mixins.json:KeyboardMixin from mod fabric-screen-api-v1->@Inject::afterKeyReleasedEvent(ILnet/minecraft/client/gui/screen/Screen;[ZIIILorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu0/topology/core_id
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu0/topology/core_id
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu0/topology/physical_package_id
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu0/topology/physical_package_id
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu0/cpu_capacity
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu1/topology/core_id
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu1/topology/core_id
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu1/topology/physical_package_id
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu1/topology/physical_package_id
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu1/cpu_capacity
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu10/topology/core_id
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu10/topology/core_id
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu10/topology/physical_package_id
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu10/topology/physical_package_id
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu10/cpu_capacity
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu11/topology/core_id
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu11/topology/core_id
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu11/topology/physical_package_id
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu11/topology/physical_package_id
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu11/cpu_capacity
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu12/topology/core_id
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu12/topology/core_id
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu12/topology/physical_package_id
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu12/topology/physical_package_id
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu12/cpu_capacity
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu13/topology/core_id
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu13/topology/core_id
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu13/topology/physical_package_id
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu13/topology/physical_package_id
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu13/cpu_capacity
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu14/topology/core_id
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu14/topology/core_id
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu14/topology/physical_package_id
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu14/topology/physical_package_id
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu14/cpu_capacity
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu15/topology/core_id
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu15/topology/core_id
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu15/topology/physical_package_id
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu15/topology/physical_package_id
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu15/cpu_capacity
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu2/topology/core_id
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu2/topology/core_id
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu2/topology/physical_package_id
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu2/topology/physical_package_id
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu2/cpu_capacity
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu3/topology/core_id
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu3/topology/core_id
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu3/topology/physical_package_id
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu3/topology/physical_package_id
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu3/cpu_capacity
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu4/topology/core_id
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu4/topology/core_id
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu4/topology/physical_package_id
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu4/topology/physical_package_id
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu4/cpu_capacity
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu5/topology/core_id
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu5/topology/core_id
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu5/topology/physical_package_id
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu5/topology/physical_package_id
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu5/cpu_capacity
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu6/topology/core_id
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu6/topology/core_id
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu6/topology/physical_package_id
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu6/topology/physical_package_id
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu6/cpu_capacity
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu7/topology/core_id
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu7/topology/core_id
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu7/topology/physical_package_id
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu7/topology/physical_package_id
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu7/cpu_capacity
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu8/topology/core_id
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu8/topology/core_id
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu8/topology/physical_package_id
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu8/topology/physical_package_id
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu8/cpu_capacity
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu9/topology/core_id
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu9/topology/core_id
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu9/topology/physical_package_id
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu9/topology/physical_package_id
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /sys/devices/system/cpu/cpu9/cpu_capacity
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /proc/cpuinfo
[16:09:00] [Render thread/DEBUG] (oshi.util.FileUtil) Reading file /proc/self/auxv
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing ReloadableResourceManagerImplMixin from fabric-resource-loader-v0.mixins.json into net.minecraft.resource.ReloadableResourceManagerImpl
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.mixins.json:ReloadableResourceManagerImplMixin from mod fabric-resource-loader-v0: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$getResourcePackNames$0(Lnet/minecraft/resource/ResourcePack;)Ljava/lang/String; to md5da3e1$fabric-resource-loader-v0$lambda$getResourcePackNames$0$0 in fabric-resource-loader-v0.mixins.json:ReloadableResourceManagerImplMixin from mod fabric-resource-loader-v0
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.mixins.json:ReloadableResourceManagerImplMixin from mod fabric-resource-loader-v0->@Inject::getResourcePackNames(Ljava/util/List;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing LifecycledResourceManagerImplMixin from fabric-resource-loader-v0.mixins.json into net.minecraft.resource.LifecycledResourceManagerImpl
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.mixins.json:LifecycledResourceManagerImplMixin from mod fabric-resource-loader-v0: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.mixins.json:LifecycledResourceManagerImplMixin from mod fabric-resource-loader-v0->@Inject::init(Lnet/minecraft/resource/ResourceType;Ljava/util/List;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.mixins.json:LifecycledResourceManagerImplMixin from mod fabric-resource-loader-v0->@Inject::init(Lnet/minecraft/resource/ResourceType;Ljava/util/List;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.mixins.json:LifecycledResourceManagerImplMixin from mod fabric-resource-loader-v0->@Inject::init(Lnet/minecraft/resource/ResourceType;Ljava/util/List;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing KeyedResourceReloadListenerClientMixin from fabric-resource-loader-v0.client.mixins.json into net.minecraft.client.resource.language.LanguageManager
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.client.mixins.json:KeyedResourceReloadListenerClientMixin from mod fabric-resource-loader-v0: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing KeyedResourceReloadListenerClientMixin from fabric-resource-loader-v0.client.mixins.json into net.minecraft.client.texture.TextureManager
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.client.mixins.json:KeyedResourceReloadListenerClientMixin from mod fabric-resource-loader-v0: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing KeyedResourceReloadListenerClientMixin from fabric-resource-loader-v0.client.mixins.json into net.minecraft.client.sound.SoundManager
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.client.mixins.json:KeyedResourceReloadListenerClientMixin from mod fabric-resource-loader-v0: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing SoundSystemMixin from fabric-sound-api-v1.mixins.json into net.minecraft.client.sound.SoundSystem
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing FontManagerMixin from fabric-resource-loader-v0.client.mixins.json into net.minecraft.client.font.FontManager
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.client.mixins.json:FontManagerMixin from mod fabric-resource-loader-v0: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing BlockColorsMixin from fabric-registry-sync-v0.client.mixins.json into net.minecraft.client.color.block.BlockColors
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.client.mixins.json:BlockColorsMixin from mod fabric-registry-sync-v0: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing BlockColorsMixin from fabric-rendering-v1.mixins.json into net.minecraft.client.color.block.BlockColors
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:BlockColorsMixin from mod fabric-rendering-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.client.mixins.json:BlockColorsMixin from mod fabric-registry-sync-v0->@Inject::create(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.client.mixins.json:BlockColorsMixin from mod fabric-registry-sync-v0->@Inject::create(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.client.mixins.json:BlockColorsMixin from mod fabric-registry-sync-v0->@Inject::create(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:BlockColorsMixin from mod fabric-rendering-v1->@Inject::create(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing ItemColorsMixin from fabric-registry-sync-v0.client.mixins.json into net.minecraft.client.color.item.ItemColors
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.client.mixins.json:ItemColorsMixin from mod fabric-registry-sync-v0: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing ItemColorsMixin from fabric-rendering-v1.mixins.json into net.minecraft.client.color.item.ItemColors
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:ItemColorsMixin from mod fabric-rendering-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.client.mixins.json:ItemColorsMixin from mod fabric-registry-sync-v0->@Inject::create(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.client.mixins.json:ItemColorsMixin from mod fabric-registry-sync-v0->@Inject::create(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.client.mixins.json:ItemColorsMixin from mod fabric-registry-sync-v0->@Inject::create(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:ItemColorsMixin from mod fabric-rendering-v1->@Inject::create(Lnet/minecraft/client/color/block/BlockColors;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing KeyedResourceReloadListenerClientMixin from fabric-resource-loader-v0.client.mixins.json into net.minecraft.client.render.model.BakedModelManager
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.client.mixins.json:KeyedResourceReloadListenerClientMixin from mod fabric-resource-loader-v0: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing BakedModelManagerMixin from fabric-model-loading-api-v1.mixins.json into net.minecraft.client.render.model.BakedModelManager
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$loadModelPluginData$0(Ljava/util/function/BiFunction;Lnet/minecraft/util/Pair;Ljava/util/List;)Lnet/minecraft/client/render/model/ModelLoader; to md5da3e1$fabric-model-loading-api-v1$lambda$loadModelPluginData$0$0 in fabric-model-loading-api-v1.mixins.json:BakedModelManagerMixin from mod fabric-model-loading-api-v1
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing TexturedRenderLayersMixin from fabric-object-builder-v1.client.mixins.json into net.minecraft.client.render.TexturedRenderLayers
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-object-builder-v1.client.mixins.json:TexturedRenderLayersMixin from mod fabric-object-builder-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-object-builder-v1.client.mixins.json:TexturedRenderLayersMixin from mod fabric-object-builder-api-v1->@Inject::modifyTextureId(Lnet/minecraft/block/WoodType;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-object-builder-v1.client.mixins.json:TexturedRenderLayersMixin from mod fabric-object-builder-api-v1->@Inject::modifyHangingTextureId(Lnet/minecraft/block/WoodType;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing KeyedResourceReloadListenerClientMixin from fabric-resource-loader-v0.client.mixins.json into net.minecraft.client.render.block.BlockRenderManager
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.client.mixins.json:KeyedResourceReloadListenerClientMixin from mod fabric-resource-loader-v0: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing BuiltinModelItemRendererMixin from fabric-rendering-v1.mixins.json into net.minecraft.client.render.item.BuiltinModelItemRenderer
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:BuiltinModelItemRendererMixin from mod fabric-rendering-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:BuiltinModelItemRendererMixin from mod fabric-rendering-v1->@Inject::fabric_onRender(Lnet/minecraft/item/ItemStack;Lnet/minecraft/client/render/model/json/ModelTransformationMode;Lnet/minecraft/client/util/math/MatrixStack;Lnet/minecraft/client/render/VertexConsumerProvider;IILorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing ItemModelsMixin from fabric-registry-sync-v0.client.mixins.json into net.minecraft.client.render.item.ItemModels
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.client.mixins.json:ItemModelsMixin from mod fabric-registry-sync-v0: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.client.mixins.json:ItemModelsMixin from mod fabric-registry-sync-v0->@Inject::onInit(Lnet/minecraft/client/render/model/BakedModelManager;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.client.mixins.json:ItemModelsMixin from mod fabric-registry-sync-v0->@Inject::onInit(Lnet/minecraft/client/render/model/BakedModelManager;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.client.mixins.json:ItemModelsMixin from mod fabric-registry-sync-v0->@Inject::onInit(Lnet/minecraft/client/render/model/BakedModelManager;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing ModelLoaderMixin from fabric-model-loading-api-v1.mixins.json into net.minecraft.client.render.model.ModelLoader
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-model-loading-api-v1.mixins.json:ModelLoaderMixin from mod fabric-model-loading-api-v1->@Inject::afterMissingModelInit(Lnet/minecraft/client/color/block/BlockColors;Lnet/minecraft/util/profiler/Profiler;Ljava/util/Map;Ljava/util/Map;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-model-loading-api-v1.mixins.json:ModelLoaderMixin from mod fabric-model-loading-api-v1->@Inject::afterMissingModelInit(Lnet/minecraft/client/color/block/BlockColors;Lnet/minecraft/util/profiler/Profiler;Ljava/util/Map;Ljava/util/Map;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-model-loading-api-v1.mixins.json:ModelLoaderMixin from mod fabric-model-loading-api-v1->@Inject::afterMissingModelInit(Lnet/minecraft/client/color/block/BlockColors;Lnet/minecraft/util/profiler/Profiler;Ljava/util/Map;Ljava/util/Map;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-model-loading-api-v1.mixins.json:ModelLoaderMixin from mod fabric-model-loading-api-v1->@Inject::fabric_preventNestedGetOrLoadModel(Lnet/minecraft/util/Identifier;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V doesn't use it's CallbackInfoReturnable
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-model-loading-api-v1.mixins.json:ModelLoaderMixin from mod fabric-model-loading-api-v1->@Inject::fabric_preventNestedGetOrLoadModel(Lnet/minecraft/util/Identifier;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V has 0 override(s) in child classes
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-model-loading-api-v1.mixins.json:ModelLoaderMixin from mod fabric-model-loading-api-v1->@Inject::fabric_preventNestedGetOrLoadModel(Lnet/minecraft/util/Identifier;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V won't be passed a CallbackInfoReturnable as a result
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-model-loading-api-v1.mixins.json:ModelLoaderMixin from mod fabric-model-loading-api-v1->@Inject::onLoadModel(Lnet/minecraft/util/Identifier;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing client.BakedModelMixin from fabric-renderer-api-v1.mixins.json into net.minecraft.client.render.model.BakedModel
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-renderer-api-v1.mixins.json:client.BakedModelMixin from mod fabric-renderer-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing BakedModelMixin from fabric-renderer-indigo.mixins.json into net.minecraft.client.render.model.BakedModel
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-renderer-indigo.mixins.json:BakedModelMixin from mod fabric-renderer-indigo: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing BlockModelRendererMixin from fabric-renderer-indigo.mixins.json into net.minecraft.client.render.block.BlockModelRenderer
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-renderer-indigo.mixins.json:BlockModelRendererMixin from mod fabric-renderer-indigo: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-renderer-indigo.mixins.json:BlockModelRendererMixin from mod fabric-renderer-indigo->@Inject::hookRender(Lnet/minecraft/world/BlockRenderView;Lnet/minecraft/client/render/model/BakedModel;Lnet/minecraft/block/BlockState;Lnet/minecraft/util/math/BlockPos;Lnet/minecraft/client/util/math/MatrixStack;Lnet/minecraft/client/render/VertexConsumer;ZLnet/minecraft/util/math/random/Random;JILorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-renderer-indigo.mixins.json:BlockModelRendererMixin from mod fabric-renderer-indigo->@Inject::onInit(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-renderer-indigo.mixins.json:BlockModelRendererMixin from mod fabric-renderer-indigo->@Inject::onInit(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-renderer-indigo.mixins.json:BlockModelRendererMixin from mod fabric-renderer-indigo->@Inject::onInit(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing FluidRendererMixin from fabric-rendering-fluids-v1.mixins.json into net.minecraft.client.render.block.FluidRenderer
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-fluids-v1.mixins.json:FluidRendererMixin from mod fabric-rendering-fluids-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$new$0()Ljava/lang/Boolean; to md5da3e1$fabric-rendering-fluids-v1$lambda$new$0$0 in fabric-rendering-fluids-v1.mixins.json:FluidRendererMixin from mod fabric-rendering-fluids-v1
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-fluids-v1.mixins.json:FluidRendererMixin from mod fabric-rendering-fluids-v1->@Inject::onResourceReloadReturn(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-fluids-v1.mixins.json:FluidRendererMixin from mod fabric-rendering-fluids-v1->@Inject::onResourceReloadReturn(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-fluids-v1.mixins.json:FluidRendererMixin from mod fabric-rendering-fluids-v1->@Inject::onResourceReloadReturn(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-fluids-v1.mixins.json:FluidRendererMixin from mod fabric-rendering-fluids-v1->@Inject::tesselate(Lnet/minecraft/world/BlockRenderView;Lnet/minecraft/util/math/BlockPos;Lnet/minecraft/client/render/VertexConsumer;Lnet/minecraft/block/BlockState;Lnet/minecraft/fluid/FluidState;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-fluids-v1.mixins.json:FluidRendererMixin from mod fabric-rendering-fluids-v1->@Inject::tesselateReturn(Lnet/minecraft/world/BlockRenderView;Lnet/minecraft/util/math/BlockPos;Lnet/minecraft/client/render/VertexConsumer;Lnet/minecraft/block/BlockState;Lnet/minecraft/fluid/FluidState;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-fluids-v1.mixins.json:FluidRendererMixin from mod fabric-rendering-fluids-v1->@Inject::tesselateReturn(Lnet/minecraft/world/BlockRenderView;Lnet/minecraft/util/math/BlockPos;Lnet/minecraft/client/render/VertexConsumer;Lnet/minecraft/block/BlockState;Lnet/minecraft/fluid/FluidState;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-fluids-v1.mixins.json:FluidRendererMixin from mod fabric-rendering-fluids-v1->@Inject::tesselateReturn(Lnet/minecraft/world/BlockRenderView;Lnet/minecraft/util/math/BlockPos;Lnet/minecraft/client/render/VertexConsumer;Lnet/minecraft/block/BlockState;Lnet/minecraft/fluid/FluidState;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-fluids-v1.mixins.json:FluidRendererMixin from mod fabric-rendering-fluids-v1->@Inject::tesselateReturn(Lnet/minecraft/world/BlockRenderView;Lnet/minecraft/util/math/BlockPos;Lnet/minecraft/client/render/VertexConsumer;Lnet/minecraft/block/BlockState;Lnet/minecraft/fluid/FluidState;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-fluids-v1.mixins.json:FluidRendererMixin from mod fabric-rendering-fluids-v1->@Inject::tesselateReturn(Lnet/minecraft/world/BlockRenderView;Lnet/minecraft/util/math/BlockPos;Lnet/minecraft/client/render/VertexConsumer;Lnet/minecraft/block/BlockState;Lnet/minecraft/fluid/FluidState;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-fluids-v1.mixins.json:FluidRendererMixin from mod fabric-rendering-fluids-v1->@Inject::tesselateReturn(Lnet/minecraft/world/BlockRenderView;Lnet/minecraft/util/math/BlockPos;Lnet/minecraft/client/render/VertexConsumer;Lnet/minecraft/block/BlockState;Lnet/minecraft/fluid/FluidState;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing HeldItemRendererMixin from fabric-item-api-v1.client.mixins.json into net.minecraft.client.render.item.HeldItemRenderer
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-item-api-v1.client.mixins.json:HeldItemRendererMixin from mod fabric-item-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-item-api-v1.client.mixins.json:HeldItemRendererMixin from mod fabric-item-api-v1->@Inject::modifyProgressAnimation(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-item-api-v1.client.mixins.json:HeldItemRendererMixin from mod fabric-item-api-v1->@Inject::modifyProgressAnimation(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-item-api-v1.client.mixins.json:HeldItemRendererMixin from mod fabric-item-api-v1->@Inject::modifyProgressAnimation(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing KeyedResourceReloadListenerClientMixin from fabric-resource-loader-v0.client.mixins.json into net.minecraft.client.render.WorldRenderer
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.client.mixins.json:KeyedResourceReloadListenerClientMixin from mod fabric-resource-loader-v0: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing WorldRendererMixin from fabric-rendering-v1.mixins.json into net.minecraft.client.render.WorldRenderer
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::beforeRender(Lnet/minecraft/client/util/math/MatrixStack;FJZLnet/minecraft/client/render/Camera;Lnet/minecraft/client/render/GameRenderer;Lnet/minecraft/client/render/LightmapTextureManager;Lorg/joml/Matrix4f;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::beforeRender(Lnet/minecraft/client/util/math/MatrixStack;FJZLnet/minecraft/client/render/Camera;Lnet/minecraft/client/render/GameRenderer;Lnet/minecraft/client/render/LightmapTextureManager;Lorg/joml/Matrix4f;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::beforeRender(Lnet/minecraft/client/util/math/MatrixStack;FJZLnet/minecraft/client/render/Camera;Lnet/minecraft/client/render/GameRenderer;Lnet/minecraft/client/render/LightmapTextureManager;Lorg/joml/Matrix4f;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::afterTerrainSetup(Lnet/minecraft/client/render/Camera;Lnet/minecraft/client/render/Frustum;ZZLorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::afterTerrainSetup(Lnet/minecraft/client/render/Camera;Lnet/minecraft/client/render/Frustum;ZZLorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::afterTerrainSetup(Lnet/minecraft/client/render/Camera;Lnet/minecraft/client/render/Frustum;ZZLorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::afterTerrainSolid(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::afterTerrainSolid(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::afterTerrainSolid(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::afterEntities(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::afterEntities(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::afterEntities(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::beforeRenderOutline(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::beforeRenderOutline(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::beforeRenderOutline(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::onDrawBlockOutline(Lnet/minecraft/client/util/math/MatrixStack;Lnet/minecraft/client/render/VertexConsumer;Lnet/minecraft/entity/Entity;DDDLnet/minecraft/util/math/BlockPos;Lnet/minecraft/block/BlockState;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::beforeDebugRender(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::beforeDebugRender(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::beforeDebugRender(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::onRenderParticles(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::onRenderParticles(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::onRenderParticles(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::onRenderParticles(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::onRenderParticles(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::onRenderParticles(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::beforeClouds(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::beforeClouds(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::beforeClouds(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::beforeClouds(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::beforeClouds(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::beforeClouds(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::beforeClouds(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::beforeClouds(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::beforeClouds(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::beforeClouds(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::beforeClouds(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::beforeClouds(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::onChunkDebugRender(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::onChunkDebugRender(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::onChunkDebugRender(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::afterRender(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::afterRender(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::afterRender(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::onReload(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::onReload(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::onReload(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::renderWeather(Lnet/minecraft/client/render/LightmapTextureManager;FDDDLorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::renderCloud(Lnet/minecraft/client/util/math/MatrixStack;Lorg/joml/Matrix4f;FDDDLorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:WorldRendererMixin from mod fabric-rendering-v1->@Inject::renderSky(Lnet/minecraft/client/util/math/MatrixStack;Lorg/joml/Matrix4f;FLnet/minecraft/client/render/Camera;ZLjava/lang/Runnable;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[16:09:00] [Render thread/DEBUG] (com.mojang.blaze3d.systems.RenderSystem) Growing IndexBuffer: Old limit 0, new limit 9360.
[16:09:00] [Render thread/DEBUG] (com.mojang.blaze3d.systems.RenderSystem) Growing IndexBuffer: Old limit 0, new limit 20.
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing ParticleManagerMixin from fabric-registry-sync-v0.client.mixins.json into net.minecraft.client.particle.ParticleManager
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.client.mixins.json:ParticleManagerMixin from mod fabric-registry-sync-v0: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing ParticleManagerMixin from fabric-particles-v1.client.mixins.json into net.minecraft.client.particle.ParticleManager
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-particles-v1.client.mixins.json:ParticleManagerMixin from mod fabric-particles-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing ParticleManagerAccessor from fabric-particles-v1.client.mixins.json into net.minecraft.client.particle.ParticleManager
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-particles-v1.client.mixins.json:ParticleManagerAccessor from mod fabric-particles-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.client.mixins.json:ParticleManagerMixin from mod fabric-registry-sync-v0->@Inject::onInit(Lnet/minecraft/client/world/ClientWorld;Lnet/minecraft/client/texture/TextureManager;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.client.mixins.json:ParticleManagerMixin from mod fabric-registry-sync-v0->@Inject::onInit(Lnet/minecraft/client/world/ClientWorld;Lnet/minecraft/client/texture/TextureManager;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-registry-sync-v0.client.mixins.json:ParticleManagerMixin from mod fabric-registry-sync-v0->@Inject::onInit(Lnet/minecraft/client/world/ClientWorld;Lnet/minecraft/client/texture/TextureManager;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-particles-v1.client.mixins.json:ParticleManagerMixin from mod fabric-particles-v1->@Inject::onRegisterDefaultFactories(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-particles-v1.client.mixins.json:ParticleManagerMixin from mod fabric-particles-v1->@Inject::onRegisterDefaultFactories(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-particles-v1.client.mixins.json:ParticleManagerMixin from mod fabric-particles-v1->@Inject::onRegisterDefaultFactories(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing BlockDustParticleMixin from fabric-particles-v1.client.mixins.json into net.minecraft.client.particle.BlockDustParticle
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-particles-v1.client.mixins.json:BlockDustParticleMixin from mod fabric-particles-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing ParticleManagerAccessor$SimpleSpriteProviderAccessor from fabric-particles-v1.client.mixins.json into net.minecraft.client.particle.ParticleManager$SimpleSpriteProvider
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-particles-v1.client.mixins.json:ParticleManagerAccessor$SimpleSpriteProviderAccessor from mod fabric-particles-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing InGameHudMixin from pokecobbleclaim.mixins.json into net.minecraft.client.gui.hud.InGameHud
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing InGameHudMixin from fabric-rendering-v1.mixins.json into net.minecraft.client.gui.hud.InGameHud
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:InGameHudMixin from mod fabric-rendering-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) pokecobbleclaim.mixins.json:InGameHudMixin from mod pokecobbleclaim->@Inject::onRenderHotbar(FLnet/minecraft/client/gui/DrawContext;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) pokecobbleclaim.mixins.json:InGameHudMixin from mod pokecobbleclaim->@Inject::onRenderCrosshair(Lnet/minecraft/client/gui/DrawContext;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) pokecobbleclaim.mixins.json:InGameHudMixin from mod pokecobbleclaim->@Inject::onRenderStatusBars(Lnet/minecraft/client/gui/DrawContext;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) pokecobbleclaim.mixins.json:InGameHudMixin from mod pokecobbleclaim->@Inject::onRenderExperienceBar(Lnet/minecraft/client/gui/DrawContext;ILorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) pokecobbleclaim.mixins.json:InGameHudMixin from mod pokecobbleclaim->@Inject::onRenderMountHealth(Lnet/minecraft/client/gui/DrawContext;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) pokecobbleclaim.mixins.json:InGameHudMixin from mod pokecobbleclaim->@Inject::onRenderScoreboardSidebar(Lnet/minecraft/client/gui/DrawContext;Lnet/minecraft/scoreboard/ScoreboardObjective;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:InGameHudMixin from mod fabric-rendering-v1->@Inject::render(Lnet/minecraft/client/gui/DrawContext;FLorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:InGameHudMixin from mod fabric-rendering-v1->@Inject::render(Lnet/minecraft/client/gui/DrawContext;FLorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:InGameHudMixin from mod fabric-rendering-v1->@Inject::render(Lnet/minecraft/client/gui/DrawContext;FLorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing DebugHudMixin from fabric-renderer-api-v1.debughud.mixins.json into net.minecraft.client.gui.hud.DebugHud
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-renderer-api-v1.debughud.mixins.json:DebugHudMixin from mod fabric-renderer-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-renderer-api-v1.debughud.mixins.json:DebugHudMixin from mod fabric-renderer-api-v1->@Inject::getLeftText(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-renderer-api-v1.debughud.mixins.json:DebugHudMixin from mod fabric-renderer-api-v1->@Inject::getLeftText(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing shader.ShaderProgramImportProcessorMixin from fabric-rendering-v1.mixins.json into net.minecraft.client.gl.ShaderProgram$1
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.ShaderProgramImportProcessorMixin from mod fabric-rendering-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.ShaderProgramImportProcessorMixin from mod fabric-rendering-v1->@Inject::captureImport(ZLjava/lang/String;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V doesn't use it's CallbackInfoReturnable
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.ShaderProgramImportProcessorMixin from mod fabric-rendering-v1->@Inject::captureImport(ZLjava/lang/String;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V has 0 override(s) in child classes
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.ShaderProgramImportProcessorMixin from mod fabric-rendering-v1->@Inject::captureImport(ZLjava/lang/String;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V won't be passed a CallbackInfoReturnable as a result
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.ShaderProgramImportProcessorMixin from mod fabric-rendering-v1->@Inject::uncaptureImport(ZLjava/lang/String;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V doesn't use it's CallbackInfoReturnable
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.ShaderProgramImportProcessorMixin from mod fabric-rendering-v1->@Inject::uncaptureImport(ZLjava/lang/String;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V has 0 override(s) in child classes
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.ShaderProgramImportProcessorMixin from mod fabric-rendering-v1->@Inject::uncaptureImport(ZLjava/lang/String;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V won't be passed a CallbackInfoReturnable as a result
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.ShaderProgramImportProcessorMixin from mod fabric-rendering-v1->@Inject::uncaptureImport(ZLjava/lang/String;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V doesn't use it's CallbackInfoReturnable
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.ShaderProgramImportProcessorMixin from mod fabric-rendering-v1->@Inject::uncaptureImport(ZLjava/lang/String;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V has 0 override(s) in child classes
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.ShaderProgramImportProcessorMixin from mod fabric-rendering-v1->@Inject::uncaptureImport(ZLjava/lang/String;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V won't be passed a CallbackInfoReturnable as a result
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.ShaderProgramImportProcessorMixin from mod fabric-rendering-v1->@Inject::uncaptureImport(ZLjava/lang/String;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V doesn't use it's CallbackInfoReturnable
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.ShaderProgramImportProcessorMixin from mod fabric-rendering-v1->@Inject::uncaptureImport(ZLjava/lang/String;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V has 0 override(s) in child classes
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:shader.ShaderProgramImportProcessorMixin from mod fabric-rendering-v1->@Inject::uncaptureImport(ZLjava/lang/String;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V won't be passed a CallbackInfoReturnable as a result
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing ResourceMixin from fabric-resource-loader-v0.mixins.json into net.minecraft.resource.Resource
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.mixins.json:ResourceMixin from mod fabric-resource-loader-v0: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing MessageHandlerMixin from fabric-message-api-v1.client.mixins.json into net.minecraft.client.network.message.MessageHandler
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-message-api-v1.client.mixins.json:MessageHandlerMixin from mod fabric-message-api-v1->@Inject::fabric_onSignedChatMessage(Lnet/minecraft/network/message/MessageType$Parameters;Lnet/minecraft/network/message/SignedMessage;Lnet/minecraft/text/Text;Lcom/mojang/authlib/GameProfile;ZLjava/time/Instant;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-message-api-v1.client.mixins.json:MessageHandlerMixin from mod fabric-message-api-v1->@Inject::fabric_onFilteredSignedChatMessage(Lnet/minecraft/network/message/MessageType$Parameters;Lnet/minecraft/network/message/SignedMessage;Lnet/minecraft/text/Text;Lcom/mojang/authlib/GameProfile;ZLjava/time/Instant;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-message-api-v1.client.mixins.json:MessageHandlerMixin from mod fabric-message-api-v1->@Inject::fabric_onProfilelessChatMessage(Lnet/minecraft/network/message/MessageType$Parameters;Lnet/minecraft/text/Text;Ljava/time/Instant;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-message-api-v1.client.mixins.json:MessageHandlerMixin from mod fabric-message-api-v1->@Inject::fabric_allowGameMessage(Lnet/minecraft/text/Text;ZLorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[16:09:00] [Render thread/INFO] (net.minecraft.resource.ReloadableResourceManagerImpl) Reloading ResourceManager: vanilla, fabric (fabric-events-lifecycle-v0, fabric-convention-tags-v1, fabric-dimensions-v1, fabric-item-group-api-v1, fabric-object-builder-api-v1, pokecobbleclaim, fabric-block-view-api-v2, fabric-lifecycle-events-v1, fabric-resource-conditions-api-v1, fabric-screen-api-v1, fabric-gametest-api-v1, fabricloader, fabric-transitive-access-wideners-v1, fabric-client-tags-api-v1, fabric-rendering-data-attachment-v1, fabric-sound-api-v1, fabric-message-api-v1, fabric-renderer-api-v1, fabric-entity-events-v1, fabric-game-rule-api-v1, fabric-api, fabric-registry-sync-v0, fabric-block-api-v1, fabric-rendering-v0, fabric-biome-api-v1, fabric-keybindings-v0, fabric-renderer-indigo, fabric-resource-loader-v0, fabric-key-binding-api-v1, fabric-recipe-api-v1, fabric-models-v0, fabric-transfer-api-v1, fabric-renderer-registries-v1, fabric-content-registries-v0, fabric-loot-tables-v1, fabric-model-loading-api-v1, fabric-networking-api-v1, fabric-data-attachment-api-v1, fabric-mining-level-api-v1, fabric-command-api-v1, fabric-networking-v0, fabric-command-api-v2, fabric-crash-report-info-v1, fabric-api-lookup-api-v1, fabric-item-api-v1, fabric-blockrenderlayer-v1, fabric-loot-api-v2, fabric-screen-handler-api-v1, fabric-data-generation-api-v1, fabric-api-base, fabric-particles-v1, fabric-events-interaction-v0, fabric-commands-v0, fabric-rendering-v1, fabric-rendering-fluids-v1, fabric-containers-v0)
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing NamespaceResourceManagerMixin from fabric-resource-loader-v0.mixins.json into net.minecraft.resource.NamespaceResourceManager
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.mixins.json:NamespaceResourceManagerMixin from mod fabric-resource-loader-v0: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.mixins.json:NamespaceResourceManagerMixin from mod fabric-resource-loader-v0->@Inject::onGetAllResources(Lnet/minecraft/util/Identifier;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;Lnet/minecraft/util/Identifier;Ljava/util/List;)V doesn't use it's CallbackInfoReturnable
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.mixins.json:NamespaceResourceManagerMixin from mod fabric-resource-loader-v0->@Inject::onGetAllResources(Lnet/minecraft/util/Identifier;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;Lnet/minecraft/util/Identifier;Ljava/util/List;)V has 0 override(s) in child classes
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.mixins.json:NamespaceResourceManagerMixin from mod fabric-resource-loader-v0->@Inject::onGetAllResources(Lnet/minecraft/util/Identifier;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;Lnet/minecraft/util/Identifier;Ljava/util/List;)V won't be passed a CallbackInfoReturnable as a result
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing SimpleResourceReloadMixin from fabric-resource-loader-v0.mixins.json into net.minecraft.resource.SimpleResourceReload
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.mixins.json:SimpleResourceReloadMixin from mod fabric-resource-loader-v0: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:09:00] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-resource-loader-v0.mixins.json:SimpleResourceReloadMixin from mod fabric-resource-loader-v0->@Inject::method_40087(Lnet/minecraft/resource/ResourceManager;Ljava/util/List;Ljava/util/concurrent/Executor;Ljava/util/concurrent/Executor;Ljava/util/concurrent/CompletableFuture;ZLorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V doesn't use it's CallbackInfoReturnable
[16:09:00] [Worker-Main-6/INFO] (net.minecraft.client.font.UnihexFont) Found unifont_all_no_pua-15.0.06.hex, loading
[16:09:01] [Realms Notification Availability checker #1/INFO] (net.minecraft.client.realms.RealmsClient) Could not authorize you against Realms server: java.lang.RuntimeException: Failed to parse into SignedJWT: FabricMC
[16:09:01] [Worker-Main-15/DEBUG] (FabricLoader/Mixin) Mixing ModelLoaderBakerImplMixin from fabric-model-loading-api-v1.mixins.json into net.minecraft.client.render.model.ModelLoader$BakerImpl
[16:09:01] [Worker-Main-15/DEBUG] (FabricLoader/Mixin) Mixing client.MultipartBakedModelMixin from fabric-renderer-api-v1.mixins.json into net.minecraft.client.render.model.MultipartBakedModel
[16:09:01] [Worker-Main-15/DEBUG] (FabricLoader/Mixin) fabric-renderer-api-v1.mixins.json:client.MultipartBakedModelMixin from mod fabric-renderer-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:09:01] [Worker-Main-15/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$emitBlockQuads$0(Lnet/minecraft/util/math/random/Random;J)Lnet/minecraft/util/math/random/Random; to md5da3e1$fabric-renderer-api-v1$lambda$emitBlockQuads$0$0 in fabric-renderer-api-v1.mixins.json:client.MultipartBakedModelMixin from mod fabric-renderer-api-v1
[16:09:01] [Worker-Main-15/DEBUG] (FabricLoader/Mixin) fabric-renderer-api-v1.mixins.json:client.MultipartBakedModelMixin from mod fabric-renderer-api-v1->@Inject::onInit(Ljava/util/List;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:09:01] [Worker-Main-15/DEBUG] (FabricLoader/Mixin) fabric-renderer-api-v1.mixins.json:client.MultipartBakedModelMixin from mod fabric-renderer-api-v1->@Inject::onInit(Ljava/util/List;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:09:01] [Worker-Main-15/DEBUG] (FabricLoader/Mixin) fabric-renderer-api-v1.mixins.json:client.MultipartBakedModelMixin from mod fabric-renderer-api-v1->@Inject::onInit(Ljava/util/List;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:09:01] [Worker-Main-15/DEBUG] (FabricLoader/Mixin) Mixing client.WeightedBakedModelMixin from fabric-renderer-api-v1.mixins.json into net.minecraft.client.render.model.WeightedBakedModel
[16:09:01] [Worker-Main-15/DEBUG] (FabricLoader/Mixin) fabric-renderer-api-v1.mixins.json:client.WeightedBakedModelMixin from mod fabric-renderer-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:09:01] [Worker-Main-15/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$emitItemQuads$1(Ljava/util/function/Supplier;)Lnet/minecraft/util/math/random/Random; to md5da3e1$fabric-renderer-api-v1$lambda$emitItemQuads$1$0 in fabric-renderer-api-v1.mixins.json:client.WeightedBakedModelMixin from mod fabric-renderer-api-v1
[16:09:01] [Worker-Main-15/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$emitBlockQuads$0(Ljava/util/function/Supplier;)Lnet/minecraft/util/math/random/Random; to md5da3e1$fabric-renderer-api-v1$lambda$emitBlockQuads$0$1 in fabric-renderer-api-v1.mixins.json:client.WeightedBakedModelMixin from mod fabric-renderer-api-v1
[16:09:01] [Worker-Main-15/DEBUG] (FabricLoader/Mixin) fabric-renderer-api-v1.mixins.json:client.WeightedBakedModelMixin from mod fabric-renderer-api-v1->@Inject::onInit(Ljava/util/List;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:09:01] [Worker-Main-15/DEBUG] (FabricLoader/Mixin) fabric-renderer-api-v1.mixins.json:client.WeightedBakedModelMixin from mod fabric-renderer-api-v1->@Inject::onInit(Ljava/util/List;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V has 0 override(s) in child classes
[16:09:01] [Worker-Main-15/DEBUG] (FabricLoader/Mixin) fabric-renderer-api-v1.mixins.json:client.WeightedBakedModelMixin from mod fabric-renderer-api-v1->@Inject::onInit(Ljava/util/List;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V won't be passed a CallbackInfo as a result
[16:09:02] [Render thread/WARN] (net.minecraft.client.sound.SoundSystem) Missing sound for event: minecraft:item.goat_horn.play
[16:09:02] [Render thread/WARN] (net.minecraft.client.sound.SoundSystem) Missing sound for event: minecraft:entity.goat.screaming.horn_break
[16:09:02] [Render thread/INFO] (net.minecraft.client.sound.SoundEngine) OpenAL initialized on device Sound Blaster GC7 Analog Stereo
[16:09:02] [Render thread/INFO] (net.minecraft.client.sound.SoundSystem) Sound engine started
[16:09:02] [Render thread/INFO] (net.minecraft.client.texture.SpriteAtlasTexture) Created: 1024x512x4 minecraft:textures/atlas/blocks.png-atlas
[16:09:02] [Render thread/INFO] (net.minecraft.client.texture.SpriteAtlasTexture) Created: 256x256x4 minecraft:textures/atlas/signs.png-atlas
[16:09:02] [Render thread/INFO] (net.minecraft.client.texture.SpriteAtlasTexture) Created: 512x512x4 minecraft:textures/atlas/banner_patterns.png-atlas
[16:09:02] [Render thread/INFO] (net.minecraft.client.texture.SpriteAtlasTexture) Created: 512x512x4 minecraft:textures/atlas/shield_patterns.png-atlas
[16:09:02] [Render thread/INFO] (net.minecraft.client.texture.SpriteAtlasTexture) Created: 1024x1024x4 minecraft:textures/atlas/armor_trims.png-atlas
[16:09:02] [Render thread/INFO] (net.minecraft.client.texture.SpriteAtlasTexture) Created: 256x256x4 minecraft:textures/atlas/chest.png-atlas
[16:09:02] [Render thread/INFO] (net.minecraft.client.texture.SpriteAtlasTexture) Created: 128x64x4 minecraft:textures/atlas/decorated_pot.png-atlas
[16:09:02] [Render thread/INFO] (net.minecraft.client.texture.SpriteAtlasTexture) Created: 512x256x4 minecraft:textures/atlas/shulker_boxes.png-atlas
[16:09:02] [Render thread/INFO] (net.minecraft.client.texture.SpriteAtlasTexture) Created: 512x256x4 minecraft:textures/atlas/beds.png-atlas
[16:09:02] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing EntityModelsMixin from fabric-rendering-v1.mixins.json into net.minecraft.client.render.entity.model.EntityModels
[16:09:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:EntityModelsMixin from mod fabric-rendering-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:09:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:EntityModelsMixin from mod fabric-rendering-v1->@Inject::registerExtraModelData(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;Lcom/google/common/collect/ImmutableMap$Builder;)V doesn't use it's CallbackInfoReturnable
[16:09:02] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing EntityModelLayersMixin from fabric-object-builder-v1.client.mixins.json into net.minecraft.client.render.entity.model.EntityModelLayers
[16:09:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-object-builder-v1.client.mixins.json:EntityModelLayersMixin from mod fabric-object-builder-api-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:09:02] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing EntityModelLayersAccessor from fabric-rendering-v1.mixins.json into net.minecraft.client.render.entity.model.EntityModelLayers
[16:09:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:EntityModelLayersAccessor from mod fabric-rendering-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:09:02] [Render thread/DEBUG] (FabricLoader/Mixin) Renaming @Accessor method getLayers()Ljava/util/Set; to getLayers$fabric-rendering-v1_$md$5da3e1$0 in fabric-rendering-v1.mixins.json:EntityModelLayersAccessor from mod fabric-rendering-v1
[16:09:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-object-builder-v1.client.mixins.json:EntityModelLayersMixin from mod fabric-object-builder-api-v1->@Inject::createSign(Lnet/minecraft/block/WoodType;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[16:09:02] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-object-builder-v1.client.mixins.json:EntityModelLayersMixin from mod fabric-object-builder-api-v1->@Inject::createHangingSign(Lnet/minecraft/block/WoodType;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[16:09:03] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing BlockEntityRendererFactoriesMixin from fabric-rendering-v1.mixins.json into net.minecraft.client.render.block.entity.BlockEntityRendererFactories
[16:09:03] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:BlockEntityRendererFactoriesMixin from mod fabric-rendering-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:09:03] [Render thread/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$init$0(Lnet/minecraft/block/entity/BlockEntityType;Lnet/minecraft/client/render/block/entity/BlockEntityRendererFactory;)V to md5da3e1$fabric-rendering-v1$lambda$init$0$0 in fabric-rendering-v1.mixins.json:BlockEntityRendererFactoriesMixin from mod fabric-rendering-v1
[16:09:03] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:BlockEntityRendererFactoriesMixin from mod fabric-rendering-v1->@Inject::init(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:09:03] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing EntityRenderersMixin from fabric-rendering-v1.mixins.json into net.minecraft.client.render.entity.EntityRenderers
[16:09:03] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:EntityRenderersMixin from mod fabric-rendering-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:09:03] [Render thread/DEBUG] (FabricLoader/Mixin) Renaming synthetic method lambda$onRegisterRenderers$0(Lnet/minecraft/entity/EntityType;Lnet/minecraft/client/render/entity/EntityRendererFactory;)V to md5da3e1$fabric-rendering-v1$lambda$onRegisterRenderers$0$0 in fabric-rendering-v1.mixins.json:EntityRenderersMixin from mod fabric-rendering-v1
[16:09:03] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:EntityRenderersMixin from mod fabric-rendering-v1->@Inject::onRegisterRenderers(Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V doesn't use it's CallbackInfo
[16:09:03] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing LivingEntityRendererAccessor from fabric-rendering-v1.mixins.json into net.minecraft.client.render.entity.LivingEntityRenderer
[16:09:03] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:LivingEntityRendererAccessor from mod fabric-rendering-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:09:03] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing ArmorFeatureRendererMixin from fabric-rendering-v1.mixins.json into net.minecraft.client.render.entity.feature.ArmorFeatureRenderer
[16:09:03] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:ArmorFeatureRendererMixin from mod fabric-rendering-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:09:03] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:ArmorFeatureRendererMixin from mod fabric-rendering-v1->@Inject::renderArmor(Lnet/minecraft/client/util/math/MatrixStack;Lnet/minecraft/client/render/VertexConsumerProvider;Lnet/minecraft/entity/LivingEntity;Lnet/minecraft/entity/EquipmentSlot;ILnet/minecraft/client/render/entity/model/BipedEntityModel;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[16:09:03] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:ArmorFeatureRendererMixin from mod fabric-rendering-v1->@Inject::getArmorTexture(Lnet/minecraft/item/ArmorItem;ZLjava/lang/String;Lorg/spongepowered/asm/mixin/injection/callback/CallbackInfoReturnable;)V does use it's CallbackInfoReturnable
[16:09:03] [Render thread/DEBUG] (FabricLoader/Mixin) Mixing CapeFeatureRendererMixin from fabric-rendering-v1.mixins.json into net.minecraft.client.render.entity.feature.CapeFeatureRenderer
[16:09:03] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:CapeFeatureRendererMixin from mod fabric-rendering-v1: Class version 61 required is higher than the class version supported by the current version of Mixin (JAVA_16 supports class version 60)
[16:09:03] [Render thread/DEBUG] (FabricLoader/Mixin) fabric-rendering-v1.mixins.json:CapeFeatureRendererMixin from mod fabric-rendering-v1->@Inject::injectCapeRenderCheck(Lnet/minecraft/client/util/math/MatrixStack;Lnet/minecraft/client/render/VertexConsumerProvider;ILnet/minecraft/client/network/AbstractClientPlayerEntity;FFFFFFLorg/spongepowered/asm/mixin/injection/callback/CallbackInfo;)V does use it's CallbackInfo
[16:09:03] [Render thread/WARN] (net.minecraft.client.gl.ShaderProgram) Shader rendertype_entity_translucent_emissive could not find sampler named Sampler2 in the specified shader program.
[16:09:03] [Render thread/INFO] (net.minecraft.client.texture.SpriteAtlasTexture) Created: 256x256x0 minecraft:textures/atlas/particles.png-atlas
[16:09:03] [Render thread/INFO] (net.minecraft.client.texture.SpriteAtlasTexture) Created: 256x256x0 minecraft:textures/atlas/paintings.png-atlas
[16:09:03] [Render thread/INFO] (net.minecraft.client.texture.SpriteAtlasTexture) Created: 128x128x0 minecraft:textures/atlas/mob_effects.png-atlas
