[19:20:32] [main/INFO] (FabricLoader/GameProvider) Loading Minecraft 1.20.1 with Fabric Loader 0.16.13
[19:20:33] [main/INFO] (FabricLoader) Loading 59 mods:
	- fabric-api 0.92.5+1.20.1
	- fabric-api-base 0.4.32+1802ada577
	- fabric-api-lookup-api-v1 1.6.37+1802ada577
	- fabric-biome-api-v1 13.0.14+1802ada577
	- fabric-block-api-v1 1.0.12+1802ada577
	- fabric-block-view-api-v2 1.0.3+924f046a77
	- fabric-blockrenderlayer-v1 1.1.42+1802ada577
	- fabric-client-tags-api-v1 1.1.3+1802ada577
	- fabric-command-api-v1 1.2.35+f71b366f77
	- fabric-command-api-v2 2.2.14+1802ada577
	- fabric-commands-v0 0.2.52+df3654b377
	- fabric-containers-v0 0.1.66+df3654b377
	- fabric-content-registries-v0 4.0.13+1802ada577
	- fabric-convention-tags-v1 1.5.6+1802ada577
	- fabric-crash-report-info-v1 0.2.20+1802ada577
	- fabric-data-attachment-api-v1 1.0.2+de0fd6d177
	- fabric-data-generation-api-v1 12.3.6+1802ada577
	- fabric-dimensions-v1 2.1.55+1802ada577
	- fabric-entity-events-v1 1.6.1+1c78457f77
	- fabric-events-interaction-v0 0.6.4+13a40c6677
	- fabric-events-lifecycle-v0 0.2.64+df3654b377
	- fabric-game-rule-api-v1 1.0.41+1802ada577
	- fabric-gametest-api-v1 1.2.15+1802ada577
	- fabric-item-api-v1 2.1.29+1802ada577
	- fabric-item-group-api-v1 4.0.14+1802ada577
	- fabric-key-binding-api-v1 1.0.38+1802ada577
	- fabric-keybindings-v0 0.2.36+df3654b377
	- fabric-lifecycle-events-v1 2.2.23+1802ada577
	- fabric-loot-api-v2 1.2.3+1802ada577
	- fabric-loot-tables-v1 1.1.47+9e7660c677
	- fabric-message-api-v1 5.1.10+1802ada577
	- fabric-mining-level-api-v1 2.1.52+1802ada577
	- fabric-model-loading-api-v1 1.0.4+1802ada577
	- fabric-models-v0 0.4.3+9386d8a777
	- fabric-networking-api-v1 1.3.13+13a40c6677
	- fabric-networking-v0 0.3.53+df3654b377
	- fabric-object-builder-api-v1 11.1.5+e35120df77
	- fabric-particles-v1 1.1.3+1802ada577
	- fabric-recipe-api-v1 1.0.23+1802ada577
	- fabric-registry-sync-v0 2.3.5+1802ada577
	- fabric-renderer-api-v1 3.2.2+1802ada577
	- fabric-renderer-indigo 1.5.3+85287f9f77
	- fabric-renderer-registries-v1 3.2.47+df3654b377
	- fabric-rendering-data-attachment-v1 0.3.39+92a0d36777
	- fabric-rendering-fluids-v1 3.0.29+1802ada577
	- fabric-rendering-v0 1.1.50+df3654b377
	- fabric-rendering-v1 3.0.9+1802ada577
	- fabric-resource-conditions-api-v1 2.3.9+1802ada577
	- fabric-resource-loader-v0 0.11.12+fb82e9d777
	- fabric-screen-api-v1 2.0.9+1802ada577
	- fabric-screen-handler-api-v1 1.3.32+1802ada577
	- fabric-sound-api-v1 1.0.14+1802ada577
	- fabric-transfer-api-v1 3.3.6+8dd72ea377
	- fabric-transitive-access-wideners-v1 4.3.2+1802ada577
	- fabricloader 0.16.13
	- java 17
	- minecraft 1.20.1
	- mixinextras 0.4.1
	- pokecobbleclaim 1.0.0
[19:20:33] [main/INFO] (FabricLoader/Mixin) SpongePowered MIXIN Subsystem Version=0.8.7 Source=file:/home/<USER>/.gradle/caches/modules-2/files-2.1/net.fabricmc/sponge-mixin/0.15.4+mixin.0.8.7/6a12aacc794f1078458433116e9ed42c1cc98096/sponge-mixin-0.15.4+mixin.0.8.7.jar Service=Knot/Fabric Env=CLIENT
[19:20:33] [main/INFO] (FabricLoader/Mixin) Loaded Fabric development mappings for mixin remapper!
[19:20:33] [main/INFO] (FabricLoader/Mixin) Compatibility level set to JAVA_17
[19:20:36] [main/INFO] (FabricLoader/MixinExtras|Service) Initializing MixinExtras via com.llamalad7.mixinextras.service.MixinExtrasServiceImpl(version=0.4.1).
[19:20:43] [Datafixer Bootstrap/INFO] (Minecraft) 188 Datafixer optimizations took 266 milliseconds
[19:20:45] [Render thread/INFO] (Minecraft) Environment: authHost='https://authserver.mojang.com', accountsHost='https://api.mojang.com', sessionHost='https://sessionserver.mojang.com', servicesHost='https://api.minecraftservices.com', name='PROD'
[19:20:45] [Render thread/ERROR] (Minecraft) Failed to verify authentication
com.mojang.authlib.exceptions.InvalidCredentialsException: Status: 401
	at com.mojang.authlib.exceptions.MinecraftClientHttpException.toAuthenticationException(MinecraftClientHttpException.java:56) ~[authlib-4.0.43.jar:?]
	at com.mojang.authlib.yggdrasil.YggdrasilUserApiService.fetchProperties(YggdrasilUserApiService.java:156) ~[authlib-4.0.43.jar:?]
	at com.mojang.authlib.yggdrasil.YggdrasilUserApiService.<init>(YggdrasilUserApiService.java:55) ~[authlib-4.0.43.jar:?]
	at com.mojang.authlib.yggdrasil.YggdrasilAuthenticationService.createUserApiService(YggdrasilAuthenticationService.java:124) ~[authlib-4.0.43.jar:?]
	at net.minecraft.client.MinecraftClient.createUserApiService(MinecraftClient.java:733) ~[minecraft-merged-7787b014d4-1.20.1-net.fabricmc.yarn.1_20_1.1.20.1+build.10-v2.jar:?]
	at net.minecraft.client.MinecraftClient.<init>(MinecraftClient.java:442) ~[minecraft-merged-7787b014d4-1.20.1-net.fabricmc.yarn.1_20_1.1.20.1+build.10-v2.jar:?]
	at net.minecraft.client.main.Main.main(Main.java:211) ~[minecraft-merged-7787b014d4-1.20.1-net.fabricmc.yarn.1_20_1.1.20.1+build.10-v2.jar:?]
	at net.fabricmc.loader.impl.game.minecraft.MinecraftGameProvider.launch(MinecraftGameProvider.java:480) ~[fabric-loader-0.16.13.jar:?]
	at net.fabricmc.loader.impl.launch.knot.Knot.launch(Knot.java:74) ~[fabric-loader-0.16.13.jar:?]
	at net.fabricmc.loader.impl.launch.knot.KnotClient.main(KnotClient.java:23) ~[fabric-loader-0.16.13.jar:?]
	at net.fabricmc.devlaunchinjector.Main.main(Main.java:86) ~[dev-launch-injector-0.2.1+build.8.jar:?]
Caused by: com.mojang.authlib.exceptions.MinecraftClientHttpException: Status: 401
	at com.mojang.authlib.minecraft.client.MinecraftClient.readInputStream(MinecraftClient.java:85) ~[authlib-4.0.43.jar:?]
	at com.mojang.authlib.minecraft.client.MinecraftClient.get(MinecraftClient.java:48) ~[authlib-4.0.43.jar:?]
	at com.mojang.authlib.yggdrasil.YggdrasilUserApiService.fetchProperties(YggdrasilUserApiService.java:129) ~[authlib-4.0.43.jar:?]
	... 9 more
[19:20:45] [Render thread/INFO] (Minecraft) Setting user: Player1
[19:20:45] [Render thread/INFO] (pokecobbleclaim) Initializing PokeCobbleClaim mod
[19:20:45] [Render thread/INFO] (pokecobbleclaim) Registering client-side network handlers
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Economy client-side network handlers registered
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Registered client town jobs network handlers
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Registered ClientGlobalChunkSyncHandler network handlers
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Enhanced Image Transfer Manager initialized (client-side)
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Registering ConfigSynchronizer client-side packet handlers
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Registered CONFIG_SYNC handler: pokecobbleclaim:config_sync
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Registered USER_PREFERENCES_SYNC handler: pokecobbleclaim:user_preferences_sync
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Registering server-side network handlers
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Registered town jobs network handlers
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Registered town request network handlers
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Registered GlobalChunkSyncHandler network handlers
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Enhanced Image Transfer Manager initialized (server-side)
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Registering ConfigSynchronizer server-side packet handlers
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Registered CONFIG_REQUEST handler: pokecobbleclaim:config_request
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Registered CONFIG_UPDATE handler: pokecobbleclaim:config_update
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Reset all town and player data versions
[19:20:46] [Render thread/INFO] (pokecobbleclaim) No individual town files found
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Town data loading completed. Final town count in TownManager: 0
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Simple chunk protection handler initialized
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Simple chunk protection system initialized successfully - complex systems disabled
[19:20:46] [Render thread/INFO] (pokecobbleclaim) === Testing Simple Permission System ===
[19:20:46] [Render thread/INFO] (pokecobbleclaim) ✓ Test 1: Unclaimed chunk logic verified (allows all actions)
[19:20:46] [Render thread/INFO] (pokecobbleclaim) ✓ Test 2: Permission constants are correct
[19:20:46] [Render thread/INFO] (pokecobbleclaim) ✓ Test 3: Permission name mapping works correctly
[19:20:46] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Checking permission for rank MEMBER (Resident), permissionIndex 0
[19:20:46] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Retrieved permissions array for rank MEMBER: [true, false, false, false, false, false, false, false]
[19:20:46] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Town member permission result = true
[19:20:46] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Checking permission for rank VISITOR (Citizen), permissionIndex 0
[19:20:46] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Retrieved permissions array for rank VISITOR: [false, false, false, false, false, false, false, false]
[19:20:46] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Town member permission result = false
[19:20:46] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Checking permission for rank OWNER (Mayor), permissionIndex 0
[19:20:46] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Retrieved permissions array for rank OWNER: [true, true, true, true, true, true, true, true]
[19:20:46] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Town member permission result = true
[19:20:46] [Render thread/INFO] (pokecobbleclaim) ✓ Test 4: Individual rank permissions work correctly (non-hierarchical)
[19:20:46] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Checking permission for rank null (null), permissionIndex 1
[19:20:46] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Non-member permission result = true
[19:20:46] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Checking permission for rank null (null), permissionIndex 0
[19:20:46] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Non-member permission result = false
[19:20:46] [Render thread/INFO] (pokecobbleclaim) ✓ Test 5: Non-member permissions work correctly
[19:20:46] [Render thread/INFO] (pokecobbleclaim) === All Simple Permission System Tests PASSED ===
[19:20:46] [Render thread/INFO] (pokecobbleclaim) === Testing Individual Rank Permissions (Non-Hierarchical) ===
[19:20:46] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Checking permission for rank MEMBER (Resident), permissionIndex 2
[19:20:46] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Retrieved permissions array for rank MEMBER: [false, false, true, false, false, false, false, false]
[19:20:46] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Town member permission result = true
[19:20:46] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Checking permission for rank VISITOR (Citizen), permissionIndex 2
[19:20:46] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Retrieved permissions array for rank VISITOR: [false, false, false, false, false, false, false, false]
[19:20:46] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Town member permission result = false
[19:20:46] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Checking permission for rank MODERATOR (Council), permissionIndex 2
[19:20:46] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Retrieved permissions array for rank MODERATOR: [false, false, false, false, false, false, false, false]
[19:20:46] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Town member permission result = false
[19:20:46] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Checking permission for rank OWNER (Mayor), permissionIndex 2
[19:20:46] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Retrieved permissions array for rank OWNER: [true, true, true, true, true, true, true, true]
[19:20:46] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Town member permission result = true
[19:20:46] [Render thread/INFO] (pokecobbleclaim) ✓ Individual rank permissions test PASSED - only specified rank gets permission (plus admin ranks)
[19:20:46] [Render thread/INFO] (pokecobbleclaim) 🎉 ALL PERMISSION SYSTEM TESTS PASSED! The new simple system is working correctly.
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Claim tag sync handlers initialized
[19:20:46] [Render thread/INFO] (pokecobbleclaim) ChunkClaimPacketHandler: Starting initialization...
[19:20:46] [Render thread/INFO] (pokecobbleclaim) ChunkClaimPacketHandler: Registering server-side handler for CHUNK_CLAIM_REQUEST: pokecobbleclaim:chunk_claim_request
[19:20:46] [Render thread/INFO] (pokecobbleclaim) ChunkClaimPacketHandler: Successfully registered CHUNK_CLAIM_REQUEST handler
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Chunk claim packet handlers initialized
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Chunk claim sync handlers initialized
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Claim tool selection sync handlers initialized
[19:20:46] [Render thread/INFO] (pokecobbleclaim) PokeCobbleClaim mod initialized successfully
[19:20:46] [Render thread/INFO] (Indigo) [Indigo] Registering Indigo renderer!
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Initializing PokeCobbleClaim client
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Registering town keybinding
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Registering chunk boundary renderer
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Loaded user preferences from: pokecobbleclaim-user-preferences.json
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Client Permission Handler v2 temporarily disabled for debugging
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Initialized client-side data managers for synchronization
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Registering sounds
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Successfully registered sound: pokecobbleclaim:notification.invite
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Successfully registered sound: pokecobbleclaim:ui.button.click
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Registered sounds on client side
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Registering client-side network handlers
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Economy client-side network handlers registered
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Registered ClientGlobalChunkSyncHandler network handlers
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Enhanced Image Transfer Manager initialized (client-side)
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Registering ConfigSynchronizer client-side packet handlers
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Registered CONFIG_SYNC handler: pokecobbleclaim:config_sync
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Registered USER_PREFERENCES_SYNC handler: pokecobbleclaim:user_preferences_sync
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Registering server-side network handlers
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Registered town jobs network handlers
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Registered town request network handlers
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Registered GlobalChunkSyncHandler network handlers
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Enhanced Image Transfer Manager initialized (server-side)
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Registering ConfigSynchronizer server-side packet handlers
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Registered CONFIG_REQUEST handler: pokecobbleclaim:config_request
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Registered CONFIG_UPDATE handler: pokecobbleclaim:config_update
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Initializing default client products
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Economy client-side network handlers registered
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Registered client-side network handlers
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Initializing phone feature
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Initializing app position manager
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Initializing app position manager
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Setting up default app positions
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Initialized 24 default app positions
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Initializing app registry
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Initializing app registry
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Registered 6 apps
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Initializing phone texture manager
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Initializing and registering phone notification overlay
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Registering phone notification overlay renderer
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Registering phone notification renderer
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Registered phone notification renderer
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Registering phone keybinding
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Registering phone keybinding
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Initialized phone feature
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Registering Shape Visualizer Tool
[19:20:46] [Render thread/INFO] (pokecobbleclaim) Registered shape visualizer tool
[19:20:46] [Render thread/INFO] (pokecobbleclaim) PokeCobbleClaim client initialized successfully
[19:20:46] [Render thread/INFO] (Minecraft) Backend library: LWJGL version 3.3.1 SNAPSHOT
[19:20:47] [Render thread/INFO] (Minecraft) Reloading ResourceManager: vanilla, fabric (fabric-block-view-api-v2, fabric-resource-loader-v0, fabric-data-attachment-api-v1, fabric-content-registries-v0, fabric-screen-api-v1, fabric-events-lifecycle-v0, fabric-data-generation-api-v1, fabric-keybindings-v0, fabric-key-binding-api-v1, fabric-renderer-indigo, fabric-renderer-api-v1, fabric-rendering-v1, pokecobbleclaim, fabric-entity-events-v1, fabric-convention-tags-v1, fabric-client-tags-api-v1, fabric-message-api-v1, fabric-rendering-data-attachment-v1, fabric-transfer-api-v1, fabric-loot-tables-v1, fabric-containers-v0, fabric-biome-api-v1, fabric-networking-api-v1, fabric-transitive-access-wideners-v1, fabric-particles-v1, fabric-block-api-v1, fabric-api-base, fabric-commands-v0, fabric-rendering-fluids-v1, fabric-blockrenderlayer-v1, fabric-models-v0, fabric-renderer-registries-v1, fabricloader, fabric-api, fabric-events-interaction-v0, fabric-recipe-api-v1, fabric-api-lookup-api-v1, fabric-loot-api-v2, fabric-dimensions-v1, fabric-item-api-v1, fabric-model-loading-api-v1, fabric-game-rule-api-v1, fabric-command-api-v1, fabric-resource-conditions-api-v1, fabric-rendering-v0, fabric-sound-api-v1, fabric-networking-v0, fabric-item-group-api-v1, fabric-registry-sync-v0, fabric-command-api-v2, fabric-object-builder-api-v1, fabric-screen-handler-api-v1, fabric-crash-report-info-v1, fabric-lifecycle-events-v1, fabric-mining-level-api-v1, fabric-gametest-api-v1)
[19:20:48] [Worker-Main-2/INFO] (Minecraft) Found unifont_all_no_pua-15.0.06.hex, loading
[19:20:48] [Realms Notification Availability checker #1/INFO] (Minecraft) Could not authorize you against Realms server: java.lang.RuntimeException: Failed to parse into SignedJWT: FabricMC
[19:20:49] [Render thread/WARN] (Minecraft) Missing sound for event: minecraft:item.goat_horn.play
[19:20:49] [Render thread/WARN] (Minecraft) Missing sound for event: minecraft:entity.goat.screaming.horn_break
[19:20:50] [Render thread/INFO] (Minecraft) OpenAL initialized on device Sound Blaster GC7 Analog Stereo
[19:20:50] [Render thread/INFO] (Minecraft) Sound engine started
[19:20:50] [Render thread/INFO] (Minecraft) Created: 1024x512x4 minecraft:textures/atlas/blocks.png-atlas
[19:20:50] [Render thread/INFO] (Minecraft) Created: 256x256x4 minecraft:textures/atlas/signs.png-atlas
[19:20:50] [Render thread/INFO] (Minecraft) Created: 512x512x4 minecraft:textures/atlas/banner_patterns.png-atlas
[19:20:50] [Render thread/INFO] (Minecraft) Created: 512x512x4 minecraft:textures/atlas/shield_patterns.png-atlas
[19:20:50] [Render thread/INFO] (Minecraft) Created: 1024x1024x4 minecraft:textures/atlas/armor_trims.png-atlas
[19:20:50] [Render thread/INFO] (Minecraft) Created: 256x256x4 minecraft:textures/atlas/chest.png-atlas
[19:20:50] [Render thread/INFO] (Minecraft) Created: 128x64x4 minecraft:textures/atlas/decorated_pot.png-atlas
[19:20:50] [Render thread/INFO] (Minecraft) Created: 512x256x4 minecraft:textures/atlas/beds.png-atlas
[19:20:50] [Render thread/INFO] (Minecraft) Created: 512x256x4 minecraft:textures/atlas/shulker_boxes.png-atlas
[19:20:50] [Render thread/WARN] (Minecraft) Shader rendertype_entity_translucent_emissive could not find sampler named Sampler2 in the specified shader program.
[19:20:51] [Render thread/INFO] (Minecraft) Created: 256x256x0 minecraft:textures/atlas/particles.png-atlas
[19:20:51] [Render thread/INFO] (Minecraft) Created: 256x256x0 minecraft:textures/atlas/paintings.png-atlas
[19:20:51] [Render thread/INFO] (Minecraft) Created: 128x128x0 minecraft:textures/atlas/mob_effects.png-atlas
