Starting a Gradle Daemon, 18 busy and 1 incompatible Daemons could not be reused, use --status for details

> Configure project :
Fabric Loom: 1.10.5

> Task :compileJava UP-TO-DATE
> Task :processResources UP-TO-DATE
> Task :classes UP-TO-DATE
> Task :jar UP-TO-DATE
> Task :compileTestJava UP-TO-DATE
> Task :processIncludeJars UP-TO-DATE
> Task :remapJar UP-TO-DATE
> Task :sourcesJar UP-TO-DATE
> Task :remapSourcesJar UP-TO-DATE
> Task :assemble UP-TO-DATE
> Task :processTestResources NO-SOURCE
> Task :testClasses UP-TO-DATE
> Task :test UP-TO-DATE
> Task :validateAccessWidener NO-SOURCE
> Task :check UP-TO-DATE
> Task :build UP-TO-DATE
> Task :cleanRunClient2
> Task :generateLog4jConfig UP-TO-DATE
> Task :generateRemapClasspath UP-TO-DATE
> Task :generateDLIConfig UP-TO-DATE
> Task :configureLaunch UP-TO-DATE
> Task :downloadAssets UP-TO-DATE
> Task :configureClientLaunch UP-TO-DATE

> Task :runClient2
[34m[16:08:49][m [32m[main/INFO][m [36m(FabricLoader/GameProvider)[m [0mLoading Minecraft 1.20.1 with Fabric Loader 0.16.13
[m[34m[16:08:49][m [32m[main/INFO][m [36m(FabricLoader)[m [0mLoading 59 mods:
	- fabric-api 0.92.5*****.1
	- fabric-api-base 0.4.32+1802ada577
	- fabric-api-lookup-api-v1 1.6.37+1802ada577
	- fabric-biome-api-v1 13.0.14+1802ada577
	- fabric-block-api-v1 1.0.12+1802ada577
	- fabric-block-view-api-v2 1.0.3+924f046a77
	- fabric-blockrenderlayer-v1 1.1.42+1802ada577
	- fabric-client-tags-api-v1 1.1.3+1802ada577
	- fabric-command-api-v1 1.2.35+f71b366f77
	- fabric-command-api-v2 2.2.14+1802ada577
	- fabric-commands-v0 0.2.52+df3654b377
	- fabric-containers-v0 0.1.66+df3654b377
	- fabric-content-registries-v0 4.0.13+1802ada577
	- fabric-convention-tags-v1 1.5.6+1802ada577
	- fabric-crash-report-info-v1 0.2.20+1802ada577
	- fabric-data-attachment-api-v1 1.0.2+de0fd6d177
	- fabric-data-generation-api-v1 12.3.6+1802ada577
	- fabric-dimensions-v1 2.1.55+1802ada577
	- fabric-entity-events-v1 1.6.1+1c78457f77
	- fabric-events-interaction-v0 0.6.4+13a40c6677
	- fabric-events-lifecycle-v0 0.2.64+df3654b377
	- fabric-game-rule-api-v1 1.0.41+1802ada577
	- fabric-gametest-api-v1 1.2.15+1802ada577
	- fabric-item-api-v1 2.1.29+1802ada577
	- fabric-item-group-api-v1 4.0.14+1802ada577
	- fabric-key-binding-api-v1 1.0.38+1802ada577
	- fabric-keybindings-v0 0.2.36+df3654b377
	- fabric-lifecycle-events-v1 2.2.23+1802ada577
	- fabric-loot-api-v2 1.2.3+1802ada577
	- fabric-loot-tables-v1 1.1.47+9e7660c677
	- fabric-message-api-v1 5.1.10+1802ada577
	- fabric-mining-level-api-v1 2.1.52+1802ada577
	- fabric-model-loading-api-v1 1.0.4+1802ada577
	- fabric-models-v0 0.4.3+9386d8a777
	- fabric-networking-api-v1 1.3.13+13a40c6677
	- fabric-networking-v0 0.3.53+df3654b377
	- fabric-object-builder-api-v1 11.1.5+e35120df77
	- fabric-particles-v1 1.1.3+1802ada577
	- fabric-recipe-api-v1 1.0.23+1802ada577
	- fabric-registry-sync-v0 2.3.5+1802ada577
	- fabric-renderer-api-v1 3.2.2+1802ada577
	- fabric-renderer-indigo 1.5.3+85287f9f77
	- fabric-renderer-registries-v1 3.2.47+df3654b377
	- fabric-rendering-data-attachment-v1 0.3.39+92a0d36777
	- fabric-rendering-fluids-v1 3.0.29+1802ada577
	- fabric-rendering-v0 1.1.50+df3654b377
	- fabric-rendering-v1 3.0.9+1802ada577
	- fabric-resource-conditions-api-v1 2.3.9+1802ada577
	- fabric-resource-loader-v0 0.11.12+fb82e9d777
	- fabric-screen-api-v1 2.0.9+1802ada577
	- fabric-screen-handler-api-v1 1.3.32+1802ada577
	- fabric-sound-api-v1 1.0.14+1802ada577
	- fabric-transfer-api-v1 3.3.6+8dd72ea377
	- fabric-transitive-access-wideners-v1 4.3.2+1802ada577
	- fabricloader 0.16.13
	- java 17
	- minecraft 1.20.1
	- mixinextras 0.4.1
	- pokecobbleclaim 1.0.0
[m[34m[16:08:49][m [32m[main/INFO][m [36m(FabricLoader/Mixin)[m [0mSpongePowered MIXIN Subsystem Version=0.8.7 Source=file:/home/<USER>/.gradle/caches/modules-2/files-2.1/net.fabricmc/sponge-mixin/0.15.4+mixin.0.8.7/6a12aacc794f1078458433116e9ed42c1cc98096/sponge-mixin-0.15.4+mixin.0.8.7.jar Service=Knot/Fabric Env=CLIENT
[m[34m[16:08:49][m [32m[main/INFO][m [36m(FabricLoader/Mixin)[m [0mLoaded Fabric development mappings for mixin remapper!
[m[34m[16:08:49][m [32m[main/INFO][m [36m(FabricLoader/Mixin)[m [0mCompatibility level set to JAVA_16
[m[34m[16:08:49][m [32m[main/INFO][m [36m(FabricLoader/Mixin)[m [0mCompatibility level set to JAVA_17
[m[34m[16:08:51][m [32m[main/INFO][m [36m(FabricLoader/MixinExtras|Service)[m [0mInitializing MixinExtras via com.llamalad7.mixinextras.service.MixinExtrasServiceImpl(version=0.4.1).
[m[34m[16:08:55][m [32m[Datafixer Bootstrap/INFO][m [36m(Minecraft)[m [0m188 Datafixer optimizations took 134 milliseconds
[m[34m[16:08:57][m [32m[Render thread/INFO][m [36m(Minecraft)[m [0mEnvironment: authHost='https://authserver.mojang.com', accountsHost='https://api.mojang.com', sessionHost='https://sessionserver.mojang.com', servicesHost='https://api.minecraftservices.com', name='PROD'
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(Minecraft)[m [0mSetting user: Player2
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mInitializing PokeCobbleClaim mod
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistering client-side network handlers
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mEconomy client-side network handlers registered
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistered client town jobs network handlers
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistered ClientGlobalChunkSyncHandler network handlers
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mEnhanced Image Transfer Manager initialized (client-side)
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistering ConfigSynchronizer client-side packet handlers
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistered CONFIG_SYNC handler: pokecobbleclaim:config_sync
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistered USER_PREFERENCES_SYNC handler: pokecobbleclaim:user_preferences_sync
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistering server-side network handlers
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistered town jobs network handlers
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistered town request network handlers
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistered GlobalChunkSyncHandler network handlers
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mEnhanced Image Transfer Manager initialized (server-side)
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistering ConfigSynchronizer server-side packet handlers
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistered CONFIG_REQUEST handler: pokecobbleclaim:config_request
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistered CONFIG_UPDATE handler: pokecobbleclaim:config_update
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mReset all town and player data versions
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mNo individual town files found
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mTown data loading completed. Final town count in TownManager: 0
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSimple chunk protection handler initialized
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSimple chunk protection system initialized successfully - complex systems disabled
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0m=== Testing Simple Permission System ===
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0m✓ Test 1: Unclaimed chunk logic verified (allows all actions)
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0m✓ Test 2: Permission constants are correct
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0m✓ Test 3: Permission name mapping works correctly
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRANK PERMISSIONS DEBUG: Checking permission for rank MEMBER (Resident), permissionIndex 0
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRANK PERMISSIONS DEBUG: Retrieved permissions array for rank MEMBER: [true, false, false, false, false, false, false, false]
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRANK PERMISSIONS DEBUG: Town member permission result = true
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRANK PERMISSIONS DEBUG: Checking permission for rank VISITOR (Citizen), permissionIndex 0
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRANK PERMISSIONS DEBUG: Retrieved permissions array for rank VISITOR: [false, false, false, false, false, false, false, false]
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRANK PERMISSIONS DEBUG: Town member permission result = false
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRANK PERMISSIONS DEBUG: Checking permission for rank OWNER (Mayor), permissionIndex 0
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRANK PERMISSIONS DEBUG: Retrieved permissions array for rank OWNER: [true, true, true, true, true, true, true, true]
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRANK PERMISSIONS DEBUG: Town member permission result = true
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0m✓ Test 4: Individual rank permissions work correctly (non-hierarchical)
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRANK PERMISSIONS DEBUG: Checking permission for rank null (null), permissionIndex 1
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRANK PERMISSIONS DEBUG: Non-member permission result = true
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRANK PERMISSIONS DEBUG: Checking permission for rank null (null), permissionIndex 0
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRANK PERMISSIONS DEBUG: Non-member permission result = false
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0m✓ Test 5: Non-member permissions work correctly
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0m=== All Simple Permission System Tests PASSED ===
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0m=== Testing Individual Rank Permissions (Non-Hierarchical) ===
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRANK PERMISSIONS DEBUG: Checking permission for rank MEMBER (Resident), permissionIndex 2
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRANK PERMISSIONS DEBUG: Retrieved permissions array for rank MEMBER: [false, false, true, false, false, false, false, false]
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRANK PERMISSIONS DEBUG: Town member permission result = true
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRANK PERMISSIONS DEBUG: Checking permission for rank VISITOR (Citizen), permissionIndex 2
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRANK PERMISSIONS DEBUG: Retrieved permissions array for rank VISITOR: [false, false, false, false, false, false, false, false]
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRANK PERMISSIONS DEBUG: Town member permission result = false
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRANK PERMISSIONS DEBUG: Checking permission for rank MODERATOR (Council), permissionIndex 2
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRANK PERMISSIONS DEBUG: Retrieved permissions array for rank MODERATOR: [false, false, false, false, false, false, false, false]
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRANK PERMISSIONS DEBUG: Town member permission result = false
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRANK PERMISSIONS DEBUG: Checking permission for rank OWNER (Mayor), permissionIndex 2
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRANK PERMISSIONS DEBUG: Retrieved permissions array for rank OWNER: [true, true, true, true, true, true, true, true]
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRANK PERMISSIONS DEBUG: Town member permission result = true
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0m✓ Individual rank permissions test PASSED - only specified rank gets permission (plus admin ranks)
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0m🎉 ALL PERMISSION SYSTEM TESTS PASSED! The new simple system is working correctly.
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClaim tag sync handlers initialized
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mChunkClaimPacketHandler: Starting initialization...
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mChunkClaimPacketHandler: Registering server-side handler for CHUNK_CLAIM_REQUEST: pokecobbleclaim:chunk_claim_request
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mChunkClaimPacketHandler: Successfully registered CHUNK_CLAIM_REQUEST handler
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mChunk claim packet handlers initialized
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mChunk claim sync handlers initialized
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClaim tool selection sync handlers initialized
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mPokeCobbleClaim mod initialized successfully
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mInitializing PokeCobbleClaim client
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistering town keybinding
[m[34m[16:08:58][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistering chunk boundary renderer
[m[34m[16:08:59][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mLoaded user preferences from: pokecobbleclaim-user-preferences.json
[m[34m[16:08:59][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mClient Permission Handler v2 temporarily disabled for debugging
[m[34m[16:08:59][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mInitialized client-side data managers for synchronization
[m[34m[16:08:59][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistering sounds
[m[34m[16:08:59][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSuccessfully registered sound: pokecobbleclaim:notification.invite
[m[34m[16:08:59][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSuccessfully registered sound: pokecobbleclaim:ui.button.click
[m[34m[16:08:59][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistered sounds on client side
[m[34m[16:08:59][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistering client-side network handlers
[m[34m[16:08:59][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mEconomy client-side network handlers registered
[m[34m[16:08:59][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistered ClientGlobalChunkSyncHandler network handlers
[m[34m[16:08:59][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mEnhanced Image Transfer Manager initialized (client-side)
[m[34m[16:08:59][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistering ConfigSynchronizer client-side packet handlers
[m[34m[16:08:59][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistered CONFIG_SYNC handler: pokecobbleclaim:config_sync
[m[34m[16:08:59][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistered USER_PREFERENCES_SYNC handler: pokecobbleclaim:user_preferences_sync
[m[34m[16:08:59][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistering server-side network handlers
[m[34m[16:08:59][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistered town jobs network handlers
[m[34m[16:08:59][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistered town request network handlers
[m[34m[16:08:59][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistered GlobalChunkSyncHandler network handlers
[m[34m[16:08:59][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mEnhanced Image Transfer Manager initialized (server-side)
[m[34m[16:08:59][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistering ConfigSynchronizer server-side packet handlers
[m[34m[16:08:59][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistered CONFIG_REQUEST handler: pokecobbleclaim:config_request
[m[34m[16:08:59][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistered CONFIG_UPDATE handler: pokecobbleclaim:config_update
[m[34m[16:08:59][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mInitializing default client products
[m[34m[16:08:59][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mEconomy client-side network handlers registered
[m[34m[16:08:59][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistered client-side network handlers
[m[34m[16:08:59][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mInitializing phone feature
[m[34m[16:08:59][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mInitializing app position manager
[m[34m[16:08:59][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mInitializing app position manager
[m[34m[16:08:59][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mSetting up default app positions
[m[34m[16:08:59][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mInitialized 24 default app positions
[m[34m[16:08:59][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mInitializing app registry
[m[34m[16:08:59][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mInitializing app registry
[m[34m[16:08:59][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistered 6 apps
[m[34m[16:08:59][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mInitializing phone texture manager
[m[34m[16:08:59][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mInitializing and registering phone notification overlay
[m[34m[16:08:59][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistering phone notification overlay renderer
[m[34m[16:08:59][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistering phone notification renderer
[m[34m[16:08:59][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistered phone notification renderer
[m[34m[16:08:59][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistering phone keybinding
[m[34m[16:08:59][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistering phone keybinding
[m[34m[16:08:59][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mInitialized phone feature
[m[34m[16:08:59][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistering Shape Visualizer Tool
[m[34m[16:08:59][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mRegistered shape visualizer tool
[m[34m[16:08:59][m [32m[Render thread/INFO][m [36m(pokecobbleclaim)[m [0mPokeCobbleClaim client initialized successfully
[m[34m[16:08:59][m [32m[Render thread/INFO][m [36m(Indigo)[m [0m[Indigo] Registering Indigo renderer!
[m[34m[16:08:59][m [32m[Render thread/INFO][m [36m(Minecraft)[m [0mBackend library: LWJGL version 3.3.1 SNAPSHOT
[m[ALSOFT] (EE) Failed to set real-time priority for thread: Operation not permitted (1)
[34m[16:09:00][m [32m[Render thread/INFO][m [36m(Minecraft)[m [0mReloading ResourceManager: vanilla, fabric (fabric-events-lifecycle-v0, fabric-convention-tags-v1, fabric-dimensions-v1, fabric-item-group-api-v1, fabric-object-builder-api-v1, pokecobbleclaim, fabric-block-view-api-v2, fabric-lifecycle-events-v1, fabric-resource-conditions-api-v1, fabric-screen-api-v1, fabric-gametest-api-v1, fabricloader, fabric-transitive-access-wideners-v1, fabric-client-tags-api-v1, fabric-rendering-data-attachment-v1, fabric-sound-api-v1, fabric-message-api-v1, fabric-renderer-api-v1, fabric-entity-events-v1, fabric-game-rule-api-v1, fabric-api, fabric-registry-sync-v0, fabric-block-api-v1, fabric-rendering-v0, fabric-biome-api-v1, fabric-keybindings-v0, fabric-renderer-indigo, fabric-resource-loader-v0, fabric-key-binding-api-v1, fabric-recipe-api-v1, fabric-models-v0, fabric-transfer-api-v1, fabric-renderer-registries-v1, fabric-content-registries-v0, fabric-loot-tables-v1, fabric-model-loading-api-v1, fabric-networking-api-v1, fabric-data-attachment-api-v1, fabric-mining-level-api-v1, fabric-command-api-v1, fabric-networking-v0, fabric-command-api-v2, fabric-crash-report-info-v1, fabric-api-lookup-api-v1, fabric-item-api-v1, fabric-blockrenderlayer-v1, fabric-loot-api-v2, fabric-screen-handler-api-v1, fabric-data-generation-api-v1, fabric-api-base, fabric-particles-v1, fabric-events-interaction-v0, fabric-commands-v0, fabric-rendering-v1, fabric-rendering-fluids-v1, fabric-containers-v0)
[m[34m[16:09:00][m [32m[Worker-Main-6/INFO][m [36m(Minecraft)[m [0mFound unifont_all_no_pua-15.0.06.hex, loading
[m[34m[16:09:01][m [32m[Realms Notification Availability checker #1/INFO][m [36m(Minecraft)[m [0mCould not authorize you against Realms server: java.lang.RuntimeException: Failed to parse into SignedJWT: FabricMC
[m[34m[16:09:02][m [33m[Render thread/WARN][m [36m(Minecraft)[m [0mMissing sound for event: minecraft:item.goat_horn.play
[m[34m[16:09:02][m [33m[Render thread/WARN][m [36m(Minecraft)[m [0mMissing sound for event: minecraft:entity.goat.screaming.horn_break
[m[ALSOFT] (EE) Failed to set real-time priority for thread: Operation not permitted (1)
[34m[16:09:02][m [32m[Render thread/INFO][m [36m(Minecraft)[m [0mOpenAL initialized on device Sound Blaster GC7 Analog Stereo
[m[34m[16:09:02][m [32m[Render thread/INFO][m [36m(Minecraft)[m [0mSound engine started
[m[34m[16:09:02][m [32m[Render thread/INFO][m [36m(Minecraft)[m [0mCreated: 1024x512x4 minecraft:textures/atlas/blocks.png-atlas
[m[34m[16:09:02][m [32m[Render thread/INFO][m [36m(Minecraft)[m [0mCreated: 256x256x4 minecraft:textures/atlas/signs.png-atlas
[m[34m[16:09:02][m [32m[Render thread/INFO][m [36m(Minecraft)[m [0mCreated: 512x512x4 minecraft:textures/atlas/banner_patterns.png-atlas
[m[34m[16:09:02][m [32m[Render thread/INFO][m [36m(Minecraft)[m [0mCreated: 512x512x4 minecraft:textures/atlas/shield_patterns.png-atlas
[m[34m[16:09:02][m [32m[Render thread/INFO][m [36m(Minecraft)[m [0mCreated: 1024x1024x4 minecraft:textures/atlas/armor_trims.png-atlas
[m[34m[16:09:02][m [32m[Render thread/INFO][m [36m(Minecraft)[m [0mCreated: 256x256x4 minecraft:textures/atlas/chest.png-atlas
[m[34m[16:09:02][m [32m[Render thread/INFO][m [36m(Minecraft)[m [0mCreated: 128x64x4 minecraft:textures/atlas/decorated_pot.png-atlas
[m[34m[16:09:02][m [32m[Render thread/INFO][m [36m(Minecraft)[m [0mCreated: 512x256x4 minecraft:textures/atlas/shulker_boxes.png-atlas
[m[34m[16:09:02][m [32m[Render thread/INFO][m [36m(Minecraft)[m [0mCreated: 512x256x4 minecraft:textures/atlas/beds.png-atlas
[m[34m[16:09:03][m [33m[Render thread/WARN][m [36m(Minecraft)[m [0mShader rendertype_entity_translucent_emissive could not find sampler named Sampler2 in the specified shader program.
[m[34m[16:09:03][m [32m[Render thread/INFO][m [36m(Minecraft)[m [0mCreated: 256x256x0 minecraft:textures/atlas/particles.png-atlas
[m[34m[16:09:03][m [32m[Render thread/INFO][m [36m(Minecraft)[m [0mCreated: 256x256x0 minecraft:textures/atlas/paintings.png-atlas
[m[34m[16:09:03][m [32m[Render thread/INFO][m [36m(Minecraft)[m [0mCreated: 128x128x0 minecraft:textures/atlas/mob_effects.png-atlas
[m