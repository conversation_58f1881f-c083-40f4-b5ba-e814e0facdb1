package com.pokecobble.phone.client;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.phone.gui.FarmerAppScreen;
import net.minecraft.item.ItemStack;
import net.minecraft.item.Items;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Client-side product manager for synchronizing product data from server.
 * Maintains a cache of product data and handles real-time updates.
 */
public class ClientProductManager {
    private static ClientProductManager instance;
    
    private final Map<String, FarmerAppScreen.Product> productCache = new ConcurrentHashMap<>();
    private long lastSyncVersion = 0;
    
    private ClientProductManager() {
        // Private constructor for singleton
    }
    
    public static synchronized ClientProductManager getInstance() {
        if (instance == null) {
            instance = new ClientProductManager();
        }
        return instance;
    }
    
    /**
     * Updates a product in the cache from server sync data.
     */
    public void updateProduct(String productId, String name, int price, String description, 
                            int requiredLevel, String typeName, String iconPath, String itemId,
                            boolean onSale, int discountPercent, Map<String, Object> categoryData, 
                            long dataVersion) {
        try {
            // Parse product type
            FarmerAppScreen.ProductType type = FarmerAppScreen.ProductType.valueOf(typeName);
            
            // Create product based on whether it has an item or texture
            FarmerAppScreen.Product product;

            // Check for selected item in category data (for unlock products)
            Object selectedItemId = categoryData.get("selected_item_id");
            if (selectedItemId != null && !selectedItemId.toString().isEmpty()) {
                ItemStack selectedItemStack = createItemStackFromId(selectedItemId.toString());
                if (!selectedItemStack.isEmpty()) {
                    product = new FarmerAppScreen.Product(productId, name, description, selectedItemStack, price, type, requiredLevel);
                } else if (itemId != null && !itemId.isEmpty()) {
                    ItemStack itemStack = createItemStack(itemId);
                    product = new FarmerAppScreen.Product(productId, name, description, itemStack, price, type, requiredLevel);
                } else {
                    product = new FarmerAppScreen.Product(productId, name, description, iconPath, price, type, requiredLevel);
                }
            } else if (itemId != null && !itemId.isEmpty()) {
                ItemStack itemStack = createItemStack(itemId);
                product = new FarmerAppScreen.Product(productId, name, description, itemStack, price, type, requiredLevel);
            } else {
                product = new FarmerAppScreen.Product(productId, name, description, iconPath, price, type, requiredLevel);
            }
            
            // Set sale status
            if (onSale) {
                product.setOnSale(true, discountPercent);
            }
            
            // Store in cache
            productCache.put(productId, product);
            
            // Update sync version
            if (dataVersion > lastSyncVersion) {
                lastSyncVersion = dataVersion;
            }
            
            Pokecobbleclaim.LOGGER.debug("Updated client product cache for {}: {} (version: {})", 
                productId, name, dataVersion);
            
            // Notify any open farmer app screens
            notifyProductUpdate(productId, product);
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to update client product cache for " + productId, e);
        }
    }
    
    /**
     * Gets a product from the cache.
     */
    public FarmerAppScreen.Product getProduct(String productId) {
        return productCache.get(productId);
    }
    
    /**
     * Gets all cached products.
     */
    public Map<String, FarmerAppScreen.Product> getAllProducts() {
        return new ConcurrentHashMap<>(productCache);
    }
    
    /**
     * Gets products by type.
     */
    public java.util.List<FarmerAppScreen.Product> getProductsByType(FarmerAppScreen.ProductType type) {
        return productCache.values().stream()
                .filter(product -> product.getType() == type)
                .sorted((a, b) -> a.getName().compareTo(b.getName()))
                .toList();
    }
    
    /**
     * Checks if the cache has a product.
     */
    public boolean hasProduct(String productId) {
        return productCache.containsKey(productId);
    }
    
    /**
     * Gets the current sync version.
     */
    public long getLastSyncVersion() {
        return lastSyncVersion;
    }
    
    /**
     * Clears the product cache.
     */
    public void clearCache() {
        productCache.clear();
        lastSyncVersion = 0;
        Pokecobbleclaim.LOGGER.info("Cleared client product cache");
    }
    
    /**
     * Creates an ItemStack from an item ID.
     */
    private ItemStack createItemStack(String itemId) {
        // First try predefined mappings
        switch (itemId) {
            case "wheat_seeds": return new ItemStack(Items.WHEAT_SEEDS);
            case "carrot": return new ItemStack(Items.CARROT);
            case "potato": return new ItemStack(Items.POTATO);
            case "beetroot_seeds": return new ItemStack(Items.BEETROOT_SEEDS);
            case "wheat": return new ItemStack(Items.WHEAT);
            case "beetroot": return new ItemStack(Items.BEETROOT);
            default:
                // Try to create from full item ID
                return createItemStackFromId(itemId);
        }
    }

    /**
     * Creates an ItemStack from a full item ID (e.g., "minecraft:diamond_sword").
     */
    private ItemStack createItemStackFromId(String fullItemId) {
        try {
            net.minecraft.util.Identifier identifier = new net.minecraft.util.Identifier(fullItemId);
            net.minecraft.item.Item item = net.minecraft.registry.Registries.ITEM.get(identifier);

            if (item != null && item != Items.AIR) {
                return new ItemStack(item);
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.warn("Failed to create ItemStack from ID: {}", fullItemId);
        }

        return ItemStack.EMPTY;
    }
    
    /**
     * Notifies any open farmer app screens about product updates.
     */
    private void notifyProductUpdate(String productId, FarmerAppScreen.Product product) {
        try {
            Pokecobbleclaim.LOGGER.debug("Product update notification for {}: {}", productId, product.getName());

            // Refresh any open FarmerAppScreens
            refreshFarmerAppScreens(productId, product);

            // Refresh any open ProductEditScreens for this product
            refreshProductEditScreens(productId, product);

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to notify product update for " + productId, e);
        }
    }

    /**
     * Refreshes any open FarmerAppScreens to show updated product data.
     */
    private void refreshFarmerAppScreens(String productId, FarmerAppScreen.Product product) {
        try {
            // Get the current screen
            net.minecraft.client.MinecraftClient client = net.minecraft.client.MinecraftClient.getInstance();
            if (client == null || client.currentScreen == null) {
                return;
            }

            // Check if it's a FarmerAppScreen and refresh it
            if (client.currentScreen instanceof FarmerAppScreen) {
                FarmerAppScreen farmerScreen = (FarmerAppScreen) client.currentScreen;

                // Update the specific product in the screen
                farmerScreen.updateProduct(productId, product);

                Pokecobbleclaim.LOGGER.debug("Refreshed FarmerAppScreen for product update: {}", productId);
            }

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to refresh FarmerAppScreens", e);
        }
    }

    /**
     * Adds a new product to the cache and notifies screens.
     * This is called when a new product is created.
     */
    public void addProduct(String productId, String name, String description, int price,
                          String typeName, int requiredLevel, String iconPath, String itemId,
                          boolean onSale, int discountPercent, Map<String, Object> categoryData, long dataVersion) {
        try {
            // Use the existing updateProduct method which handles both updates and additions
            updateProduct(productId, name, price, description, requiredLevel, typeName, iconPath, itemId,
                         onSale, discountPercent, categoryData, dataVersion);

            // Additionally refresh all farmer app screens to show the new product
            refreshAllFarmerAppScreens();

            Pokecobbleclaim.LOGGER.info("Added new product to client cache: {} ({})", name, productId);

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to add new product to client cache: " + productId, e);
        }
    }

    /**
     * Refreshes all product data in any open FarmerAppScreens.
     * This is useful when multiple products are updated at once.
     */
    public void refreshAllFarmerAppScreens() {
        try {
            // Get the current screen
            net.minecraft.client.MinecraftClient client = net.minecraft.client.MinecraftClient.getInstance();
            if (client == null || client.currentScreen == null) {
                return;
            }

            // Check if it's a FarmerAppScreen and refresh all its data
            if (client.currentScreen instanceof FarmerAppScreen) {
                FarmerAppScreen farmerScreen = (FarmerAppScreen) client.currentScreen;

                // Refresh all product data in the screen
                farmerScreen.refreshProductData();

                Pokecobbleclaim.LOGGER.debug("Refreshed all product data in FarmerAppScreen");
            }

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to refresh all FarmerAppScreens", e);
        }
    }

    /**
     * Refreshes any open ProductEditScreens for the updated product.
     */
    private void refreshProductEditScreens(String productId, FarmerAppScreen.Product product) {
        try {
            // Get the current screen
            net.minecraft.client.MinecraftClient client = net.minecraft.client.MinecraftClient.getInstance();
            if (client == null || client.currentScreen == null) {
                return;
            }

            // Check if it's a ProductEditScreen for this product
            if (client.currentScreen instanceof com.pokecobble.phone.gui.ProductEditScreen) {
                com.pokecobble.phone.gui.ProductEditScreen editScreen =
                    (com.pokecobble.phone.gui.ProductEditScreen) client.currentScreen;

                // Check if this edit screen is for the updated product
                if (editScreen.getProduct().getId().equals(productId)) {
                    // Update the screen's product reference
                    editScreen.updateProduct(product);
                    Pokecobbleclaim.LOGGER.debug("Refreshed ProductEditScreen for product: {}", productId);
                }
            }

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to refresh ProductEditScreens", e);
        }
    }
    
    /**
     * Requests a full product sync from the server.
     */
    public void requestFullSync() {
        try {
            // Send a request to the server for full product data
            net.minecraft.network.PacketByteBuf buf = net.fabricmc.fabric.api.networking.v1.PacketByteBufs.create();
            buf.writeLong(lastSyncVersion); // Send our current version
            
            net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking.send(
                new net.minecraft.util.Identifier("pokecobbleclaim", "product_sync_request"), buf);
            
            Pokecobbleclaim.LOGGER.info("Requested full product sync from server (current version: {})", lastSyncVersion);
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to request product sync from server", e);
        }
    }
    
    /**
     * Initializes the client product manager with default products.
     * This is used as a fallback when server sync is not available.
     */
    public void initializeDefaults() {
        if (!productCache.isEmpty()) {
            return; // Already initialized
        }
        
        Pokecobbleclaim.LOGGER.info("Initializing default client products");
        
        // Add default products (these would normally come from server)
        addDefaultProduct("wheat_seeds", "Wheat Seeds", "High-quality wheat seeds for farming", 
                5, FarmerAppScreen.ProductType.SEED, 1, "wheat_seeds", null);
        addDefaultProduct("carrot_seeds", "Carrot Seeds", "Fresh carrot seeds with high yield", 
                3, FarmerAppScreen.ProductType.SEED, 1, "carrot", null);
        addDefaultProduct("potato_seeds", "Potato Seeds", "Premium potato seeds for large harvests", 
                4, FarmerAppScreen.ProductType.SEED, 1, "potato", null);
        addDefaultProduct("beetroot_seeds", "Beetroot Seeds", "Nutritious beetroot seeds", 
                6, FarmerAppScreen.ProductType.SEED, 2, "beetroot_seeds", null);
        
        // Food products
        addDefaultProduct("wheat", "Wheat", "Fresh harvested wheat - sell for coins", 
                8, FarmerAppScreen.ProductType.FOOD, 0, "wheat", null);
        addDefaultProduct("carrots", "Carrots", "Crunchy orange carrots - high demand", 
                6, FarmerAppScreen.ProductType.FOOD, 0, "carrot", null);
        addDefaultProduct("potatoes", "Potatoes", "Versatile potatoes - always in demand", 
                7, FarmerAppScreen.ProductType.FOOD, 0, "potato", null);
        addDefaultProduct("beetroot", "Beetroot", "Healthy beetroot - premium price", 
                10, FarmerAppScreen.ProductType.FOOD, 0, "beetroot", null);
        
        // Tools
        addDefaultProduct("iron_hoe", "Iron Hoe", "Durable iron hoe for efficient farming", 
                25, FarmerAppScreen.ProductType.TOOL, 3, null, "pokecobbleclaim:textures/phone/farmerapp/tools/iron_hoe.png");
        addDefaultProduct("diamond_hoe", "Diamond Hoe", "Premium diamond hoe - fastest farming", 
                50, FarmerAppScreen.ProductType.TOOL, 5, null, "pokecobbleclaim:textures/phone/farmerapp/tools/diamond_hoe.png");
        addDefaultProduct("watering_can", "Watering Can", "Speeds up crop growth significantly", 
                15, FarmerAppScreen.ProductType.TOOL, 2, null, "pokecobbleclaim:textures/phone/farmerapp/tools/watering_can.png");
        
        // Upgrades
        addDefaultProduct("seed_discount", "Seed Price Upgrade", "Reduce seed costs by 15% permanently", 
                100, FarmerAppScreen.ProductType.UPGRADE, 1, null, "pokecobbleclaim:textures/phone/farmerapp/upgrades/seed_discount.png");
        addDefaultProduct("sell_bonus", "Sell Price Upgrade", "Increase crop sell prices by 20%", 
                150, FarmerAppScreen.ProductType.UPGRADE, 2, null, "pokecobbleclaim:textures/phone/farmerapp/upgrades/sell_bonus.png");
        addDefaultProduct("tool_discount", "Tool Price Upgrade", "Reduce tool costs by 25% permanently", 
                200, FarmerAppScreen.ProductType.UPGRADE, 3, null, "pokecobbleclaim:textures/phone/farmerapp/upgrades/tool_discount.png");
        
        // Unlocks
        addDefaultProduct("unlock_sprinkler", "Unlock Sprinkler", "Auto-water crops in 3x3 area", 
                75, FarmerAppScreen.ProductType.UNLOCK, 2, null, "pokecobbleclaim:textures/phone/farmerapp/unlocks/sprinkler.png");
        addDefaultProduct("unlock_harvester", "Unlock Harvester", "Auto-harvest crops in 5x5 area", 
                125, FarmerAppScreen.ProductType.UNLOCK, 4, null, "pokecobbleclaim:textures/phone/farmerapp/unlocks/harvester.png");
        addDefaultProduct("unlock_fertilizer", "Unlock Fertilizer", "Double crop growth speed", 
                90, FarmerAppScreen.ProductType.UNLOCK, 3, null, "pokecobbleclaim:textures/phone/farmerapp/unlocks/fertilizer.png");
    }
    
    private void addDefaultProduct(String id, String name, String description, int price, 
                                 FarmerAppScreen.ProductType type, int requiredLevel, 
                                 String itemId, String iconPath) {
        FarmerAppScreen.Product product;
        if (itemId != null) {
            ItemStack itemStack = createItemStack(itemId);
            product = new FarmerAppScreen.Product(id, name, description, itemStack, price, type, requiredLevel);
        } else {
            product = new FarmerAppScreen.Product(id, name, description, iconPath, price, type, requiredLevel);
        }
        
        productCache.put(id, product);
    }
}
