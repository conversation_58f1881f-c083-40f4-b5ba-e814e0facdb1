package com.pokecobble.phone.network;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.phone.data.ServerProductData;
import com.pokecobble.phone.gui.FarmerAppScreen;
import com.pokecobble.phone.manager.ServerProductManager;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking;
import net.fabricmc.fabric.api.networking.v1.PacketByteBufs;
import net.fabricmc.fabric.api.networking.v1.ServerPlayNetworking;
import net.minecraft.network.PacketByteBuf;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.text.Text;
import net.minecraft.util.Identifier;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.Map;

/**
 * Handles product creation packets between client and server.
 */
public class ProductCreatePacket {
    public static final Identifier PRODUCT_CREATE_ID = new Identifier("pokecobbleclaim", "product_create");
    public static final Identifier PRODUCT_CREATE_RESPONSE_ID = new Identifier("pokecobbleclaim", "product_create_response");
    
    private static final Gson GSON = new Gson();

    /**
     * Sends a product creation request from client to server.
     *
     * @param name The name of the new product
     * @param description The description of the new product
     * @param price The price of the new product
     * @param type The type/category of the new product
     * @param requiredLevel The required level for the new product
     * @param itemId The item ID if this product is based on an ItemStack
     * @param iconPath The icon path if this product uses a custom icon
     * @param categoryData Category-specific data map
     */
    @Environment(EnvType.CLIENT)
    public static void sendToServer(String name, String description, int price, FarmerAppScreen.ProductType type,
                                  int requiredLevel, String itemId, String iconPath, Map<String, Object> categoryData) {
        PacketByteBuf buf = PacketByteBufs.create();
        buf.writeString(name);
        buf.writeString(description);
        buf.writeInt(price);
        buf.writeString(type.name());
        buf.writeInt(requiredLevel);
        buf.writeString(itemId != null ? itemId : "");
        buf.writeString(iconPath != null ? iconPath : "");

        // Serialize category data as JSON
        String categoryDataJson = categoryData != null ? GSON.toJson(categoryData) : "{}";
        buf.writeString(categoryDataJson);

        ClientPlayNetworking.send(PRODUCT_CREATE_ID, buf);
        Pokecobbleclaim.LOGGER.info("Sent product creation request for: {}", name);
    }

    /**
     * Simplified method for creating products without category data.
     */
    @Environment(EnvType.CLIENT)
    public static void sendToServer(String name, String description, int price, FarmerAppScreen.ProductType type,
                                  int requiredLevel, String itemId, String iconPath) {
        sendToServer(name, description, price, type, requiredLevel, itemId, iconPath, new HashMap<>());
    }
    
    /**
     * Registers the server-side packet handler.
     */
    public static void registerServerHandler() {
        ServerPlayNetworking.registerGlobalReceiver(PRODUCT_CREATE_ID, (server, player, handler, buf, responseSender) -> {
            // Read packet data
            String name = buf.readString();
            String description = buf.readString();
            int price = buf.readInt();
            String typeName = buf.readString();
            int requiredLevel = buf.readInt();
            String itemId = buf.readString();
            String iconPath = buf.readString();
            String categoryDataJson = buf.readString();

            // Process on server thread
            server.execute(() -> {
                handleProductCreation(player, name, description, price, typeName, requiredLevel, 
                                    itemId, iconPath, categoryDataJson);
            });
        });
    }

    /**
     * Registers the client-side response handler.
     */
    @Environment(EnvType.CLIENT)
    public static void registerClientHandler() {
        ClientPlayNetworking.registerGlobalReceiver(PRODUCT_CREATE_RESPONSE_ID, (client, handler, buf, responseSender) -> {
            boolean success = buf.readBoolean();
            String message = buf.readString();
            String productId = buf.readString();

            client.execute(() -> {
                handleProductCreateResponse(success, message, productId);
            });
        });
    }

    /**
     * Handles product creation on the server side.
     */
    private static void handleProductCreation(ServerPlayerEntity player, String name, String description, int price,
                                            String typeName, int requiredLevel, String itemId, String iconPath,
                                            String categoryDataJson) {
        try {
            // Validate player permissions (only operators can create products for now)
            if (!player.hasPermissionLevel(2)) {
                sendCreateResponse(player, false, "§cYou don't have permission to create products!", "");
                return;
            }

            // Parse product type
            FarmerAppScreen.ProductType type;
            try {
                type = FarmerAppScreen.ProductType.valueOf(typeName);
            } catch (IllegalArgumentException e) {
                sendCreateResponse(player, false, "§cInvalid product type: " + typeName, "");
                return;
            }

            // Parse category data
            Map<String, Object> categoryData = new HashMap<>();
            try {
                Type mapType = new TypeToken<Map<String, Object>>(){}.getType();
                categoryData = GSON.fromJson(categoryDataJson, mapType);
                if (categoryData == null) {
                    categoryData = new HashMap<>();
                }
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.warn("Failed to parse category data for new product {}: {}", name, e.getMessage());
                categoryData = new HashMap<>();
            }

            // Generate unique product ID
            String productId = generateProductId(name, type);

            // Create server product data
            ServerProductData product = new ServerProductData(productId, name, description, price, type, requiredLevel);
            product.setModifiedBy(player.getName().getString());
            
            // Set item ID and icon path
            if (itemId != null && !itemId.isEmpty()) {
                product.setItemId(itemId);
            }
            if (iconPath != null && !iconPath.isEmpty()) {
                product.setIconPath(iconPath);
            }

            // Set category-specific data
            for (Map.Entry<String, Object> entry : categoryData.entrySet()) {
                product.setCategoryProperty(entry.getKey(), entry.getValue());
            }

            // Set default category-specific values if not provided
            setDefaultCategoryValues(product, type, price);

            // Add product to server manager
            ServerProductManager productManager = ServerProductManager.getInstance();
            boolean success = productManager.addProduct(product);

            if (success) {
                // Send success response
                sendCreateResponse(player, true, "§aProduct created successfully: " + name, productId);

                // Broadcast the new product to all players
                ProductUpdatePacket.broadcastProductUpdate(player.getServer(), productId);

                Pokecobbleclaim.LOGGER.info("Product {} created by {}: name={}, price={}, type={}",
                    productId, player.getName().getString(), name, price, type);
            } else {
                sendCreateResponse(player, false, "§cFailed to create product: Product ID already exists!", "");
            }

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling product creation", e);
            sendCreateResponse(player, false, "§cFailed to create product: " + e.getMessage(), "");
        }
    }

    /**
     * Sends a product creation response to the client.
     */
    private static void sendCreateResponse(ServerPlayerEntity player, boolean success, String message, String productId) {
        PacketByteBuf buf = PacketByteBufs.create();
        buf.writeBoolean(success);
        buf.writeString(message);
        buf.writeString(productId);

        ServerPlayNetworking.send(player, PRODUCT_CREATE_RESPONSE_ID, buf);
    }

    /**
     * Handles product creation response on the client side.
     */
    @Environment(EnvType.CLIENT)
    private static void handleProductCreateResponse(boolean success, String message, String productId) {
        try {
            net.minecraft.client.MinecraftClient client = net.minecraft.client.MinecraftClient.getInstance();
            if (client.player != null) {
                client.player.sendMessage(Text.literal(message), false);
            }

            if (success) {
                // Request a product sync to get the new product
                ProductNetworkManager.requestProductSync();
                
                Pokecobbleclaim.LOGGER.info("Product created successfully: {}", productId);
            } else {
                Pokecobbleclaim.LOGGER.warn("Product creation failed: {}", message);
            }

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to handle product create response", e);
        }
    }

    /**
     * Generates a unique product ID based on name and type.
     */
    private static String generateProductId(String name, FarmerAppScreen.ProductType type) {
        String baseName = name.toLowerCase()
                .replaceAll("[^a-z0-9]", "_")
                .replaceAll("_+", "_")
                .replaceAll("^_|_$", "");
        
        String typePrefix = type.name().toLowerCase();
        String baseId = typePrefix + "_" + baseName;
        
        // Add timestamp to ensure uniqueness
        long timestamp = System.currentTimeMillis() % 100000; // Last 5 digits
        return baseId + "_" + timestamp;
    }

    /**
     * Sets default category-specific values for new products.
     */
    private static void setDefaultCategoryValues(ServerProductData product, FarmerAppScreen.ProductType type, int price) {
        switch (type) {
            case SEED:
                if (!product.getCategoryData().containsKey("growth_time")) {
                    product.setGrowthTime(120);
                }
                break;
            case FOOD:
                if (!product.getCategoryData().containsKey("nutrition_value")) {
                    product.setNutritionValue(5);
                }
                break;
            case TOOL:
                if (!product.getCategoryData().containsKey("durability")) {
                    product.setDurability(100);
                }
                break;
            case UPGRADE:
                if (!product.getCategoryData().containsKey("upgrade_discount_percent")) {
                    product.setUpgradeDiscountPercent(15);
                }
                break;
            case UNLOCK:
                if (!product.getCategoryData().containsKey("unlock_cost")) {
                    product.setUnlockCost(price);
                }
                break;
        }
    }
}
