package com.pokecobble.phone.gui;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.phone.FarmerEditModeManager;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.text.Text;
import net.minecraft.util.Identifier;
import net.minecraft.item.ItemStack;
import net.minecraft.item.Items;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * A modern farmer app screen with real-life inspired shop interface.
 * Features product cards, icons, images, search functionality, and smart recommendations.
 * Designed to be user-friendly with a modern e-commerce style layout.
 */
public class FarmerAppScreen extends Screen {
    private final Screen parent;

    // Panel dimensions - responsive sizing
    private int panelWidth = 600;
    private int panelHeight = 400;

    // Professional Color Palette - Modern Dark Theme
    private static final int BACKGROUND_COLOR = 0xE0000000;
    private static final int PANEL_BACKGROUND = 0xF0121212;
    private static final int GLASS_TOP_HIGHLIGHT = 0x60FFFFFF;
    private static final int GLASS_BOTTOM_SHADOW = 0x80000000;
    private static final int GLASS_CONTENT_BG = 0xF0181818;

    // Professional Text Colors
    private static final int TEXT_PRIMARY = 0xFFFFFFFF;
    private static final int TEXT_SECONDARY = 0xFFB8BCC8;
    private static final int TEXT_MUTED = 0xFF6C7293;
    private static final int TEXT_ACCENT = 0xFF4CAF50;
    private static final int TEXT_SUCCESS = 0xFF00C851;
    private static final int TEXT_WARNING = 0xFFFF8A00;
    private static final int TEXT_ERROR = 0xFFFF4444;

    // Professional UI Colors
    private static final int ACCENT_PRIMARY = 0xFF4CAF50;   // Professional green
    private static final int ACCENT_SECONDARY = 0xFF2E7D32; // Darker green
    private static final int TAB_SELECTED = 0xFF4CAF50;
    private static final int TAB_UNSELECTED = 0xFF2A2A2A;
    private static final int TAB_HOVER = 0xFF3A3A3A;
    private static final int BORDER_COLOR = 0xFF404040;
    private static final int BORDER_ACCENT = 0xFF4CAF50;

    // Improved spacing system matching ModernTownScreen
    private static final int SPACING_XS = 4;
    private static final int SPACING_SM = 8;
    private static final int SPACING_MD = 12;
    private static final int SPACING_LG = 16;
    private static final int SPACING_XL = 24;

    // Category tab dimensions - more compact
    private static final int TAB_HEIGHT = 20;
    private static final int TAB_MIN_WIDTH = 80;
    private static final int TAB_SPACING = 1;

    // Product card dimensions and layout - smaller for better fit
    private static final int CARD_WIDTH = 120;
    private static final int CARD_HEIGHT = 65; // Reduced from 75 to 65 for more compact layout
    private static final int CARD_SPACING = 8;
    private static final int ICON_SIZE = 24;
    private static final int HEADER_HEIGHT = 45;
    private static final int SEARCH_HEIGHT = 18;

    // Professional Card Colors
    private static final int CARD_BG = 0xF01E1E1E;
    private static final int CARD_HOVER_BG = 0xF0252525;
    private static final int CARD_SELECTED_BG = 0xF02A2A2A;
    private static final int CARD_BORDER = 0xFF3A3A3A;
    private static final int CARD_BORDER_HOVER = 0xFF4CAF50;
    private static final int CARD_SHADOW = 0x40000000;

    // Professional Price Colors
    private static final int PRICE_BG = 0x804CAF50;
    private static final int PRICE_TEXT = 0xFF4CAF50;
    private static final int TEXT_PRICE = 0xFF4CAF50;
    private static final int TEXT_DISCOUNT = 0xFFFF8A00;
    private static final int SALE_BADGE_BG = 0xFFFF8A00;
    private static final int LEVEL_REQ_BG = 0x80404040;

    // Categories and products
    private final List<ShopCategory> categories = new ArrayList<>();
    private ShopCategory selectedCategory = null;
    private final List<Product> allProducts = new ArrayList<>();
    private List<Product> filteredProducts = new ArrayList<>();
    private String searchQuery = "";

    // Scrolling and layout
    private int scrollOffset = 0;
    private static final int SCROLL_AMOUNT = 20;
    private int cardsPerRow = 4;
    private int visibleRows = 3;

    // Professional animations
    private long screenOpenTime = 0;
    private static final long FADE_IN_DURATION = 300; // milliseconds

    // Price update timer display
    private long lastTimerUpdate = 0;
    private String cachedTimerText = "";
    private static final long TIMER_UPDATE_INTERVAL = 1000; // Update every second

    // Price update listener
    private com.pokecobble.phone.food.client.ClientFoodPriceManager.PriceUpdateListener priceUpdateListener;

    public FarmerAppScreen(Screen parent) {
        super(Text.literal("Farmer Shop"));
        this.parent = parent;
        this.screenOpenTime = System.currentTimeMillis();

        // Setup categories first
        setupCategories();

        // Load products from server data instead of hardcoded
        loadProductsFromServerData();
        updateFilteredProducts();

        // Register price update listener
        setupPriceUpdateListener();

        // Request fresh price data from server if needed
        requestPriceDataIfNeeded();

        // Request fresh product data from server if needed
        requestProductDataIfNeeded();
    }

    private void loadProductsFromServerData() {
        allProducts.clear();

        // First, try to load products from ClientProductManager (server-synced data)
        try {
            var clientProductManager = com.pokecobble.phone.client.ClientProductManager.getInstance();
            var serverProducts = clientProductManager.getAllProducts();

            if (!serverProducts.isEmpty()) {
                // Use server-synced products
                for (var product : serverProducts.values()) {
                    allProducts.add(product);
                }
                Pokecobbleclaim.LOGGER.debug("Loaded {} products from ClientProductManager", allProducts.size());
                return; // Exit early if we have server data
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.warn("Failed to load products from ClientProductManager, using defaults", e);
        }

        // Create minimal fallback products when server data is unavailable
        // This ensures the app still functions even without server sync
        createFallbackProducts();
        Pokecobbleclaim.LOGGER.debug("Using fallback product data - {} products created", allProducts.size());
    }

    /**
     * Creates minimal fallback products when server data is unavailable.
     * This ensures the app still functions even without server sync.
     */
    private void createFallbackProducts() {
        // Essential seed products
        allProducts.add(new Product("wheat_seeds", "Wheat Seeds", "Basic wheat seeds",
            new ItemStack(Items.WHEAT_SEEDS), 5, ProductType.SEED, 1));
        allProducts.add(new Product("carrot_seeds", "Carrot Seeds", "Basic carrot seeds",
            new ItemStack(Items.CARROT), 3, ProductType.SEED, 1));

        // Essential food products
        allProducts.add(new Product("wheat", "Wheat", "Harvested wheat",
            new ItemStack(Items.WHEAT), 8, ProductType.FOOD, 0));
        allProducts.add(new Product("carrots", "Carrots", "Fresh carrots",
            new ItemStack(Items.CARROT), 6, ProductType.FOOD, 0));

        // Basic tool
        allProducts.add(new Product("basic_hoe", "Basic Hoe", "Standard farming hoe",
            "pokecobbleclaim:textures/phone/farmerapp/tools/basic_hoe.png", 25, ProductType.TOOL, 1));

        // Basic upgrade
        allProducts.add(new Product("seed_discount", "Seed Discount", "Reduce seed costs",
            "pokecobbleclaim:textures/phone/farmerapp/upgrades/seed_discount.png", 100, ProductType.UPGRADE, 1));
    }

    private void setupCategories() {
        categories.clear();

        // Create shop categories with gamification
        categories.add(new ShopCategory("upgrades", "Upgrades", "Enhance farming & unlock tools", 0x80FF6B35));
        categories.add(new ShopCategory("seeds", "Seeds", "Purchase seeds for planting", 0x8055AA55));
        categories.add(new ShopCategory("food", "Sell Food", "Sell your harvested crops", 0x80FFAA55));
        categories.add(new ShopCategory("tools", "Tools", "Farming tools and equipment", 0x8055AAFF));
        categories.add(new ShopCategory("stats", "Statistics", "View your farming progress", 0x80AA5555));

        // Select first category by default
        if (!categories.isEmpty()) {
            selectedCategory = categories.get(0);
        }
    }

    private void updateFilteredProducts() {
        filteredProducts.clear();

        for (Product product : allProducts) {
            boolean matchesCategory =
                (selectedCategory.getId().equals("seeds") && product.getType() == ProductType.SEED) ||
                (selectedCategory.getId().equals("food") && product.getType() == ProductType.FOOD) ||
                (selectedCategory.getId().equals("tools") && product.getType() == ProductType.TOOL) ||
                (selectedCategory.getId().equals("upgrades") && (product.getType() == ProductType.UPGRADE || product.getType() == ProductType.UNLOCK));

            boolean matchesSearch = searchQuery.isEmpty() ||
                product.getName().toLowerCase().contains(searchQuery.toLowerCase()) ||
                product.getDescription().toLowerCase().contains(searchQuery.toLowerCase());

            if (matchesCategory && matchesSearch && !selectedCategory.getId().equals("stats")) {
                filteredProducts.add(product);
            }
        }

        // Smart sorting: prioritize sale items, then by level requirement, then by price
        filteredProducts.sort((a, b) -> {
            // Sale items first
            if (a.isOnSale() && !b.isOnSale()) return -1;
            if (!a.isOnSale() && b.isOnSale()) return 1;

            // Then by level requirement (lower levels first for accessibility)
            if (a.getRequiredLevel() != b.getRequiredLevel()) {
                return Integer.compare(a.getRequiredLevel(), b.getRequiredLevel());
            }

            // Finally by price (cheaper first)
            return Integer.compare(a.getDiscountedPrice(), b.getDiscountedPrice());
        });

        // Ensure scroll bounds are valid after filtering
        enforceScrollBounds();
    }

    @Override
    protected void init() {
        super.init();
        Pokecobbleclaim.LOGGER.debug("Initializing FarmerAppScreen");

        // Calculate responsive dimensions
        calculateResponsiveDimensions();

        // Ensure scroll bounds are valid after initialization
        enforceScrollBounds();
    }

    /**
     * Calculates responsive panel dimensions and layout based on current screen size.
     * This method is called during initialization and when the screen is resized.
     */
    private void calculateResponsiveDimensions() {
        // Calculate responsive panel dimensions that always fit the screen
        int margin = Math.max(20, Math.min(60, width / 20)); // Dynamic margin: 5% of width, min 20px, max 60px
        int availableWidth = width - (margin * 2);
        int availableHeight = height - (margin * 2);

        // Set panel size to fit available space, with reasonable limits
        panelWidth = Math.min(availableWidth, 700);  // Max 700px wide
        panelHeight = Math.min(availableHeight, 500); // Max 500px tall

        // Ensure minimum usable sizes, but never exceed screen
        int minWidth = Math.min(320, availableWidth);   // Min 320px or available width
        int minHeight = Math.min(240, availableHeight); // Min 240px or available height

        panelWidth = Math.max(panelWidth, minWidth);
        panelHeight = Math.max(panelHeight, minHeight);

        // Final safety check - ensure panel never exceeds screen bounds
        panelWidth = Math.min(panelWidth, width - 20);
        panelHeight = Math.min(panelHeight, height - 20);

        // Update grid layout based on actual panel size with responsive card sizing
        int gridAvailableWidth = panelWidth - SPACING_MD * 2;

        // Calculate optimal cards per row based on available width
        int minCardsPerRow = 1;
        int maxCardsPerRow = Math.min(5, gridAvailableWidth / (CARD_WIDTH + CARD_SPACING));
        cardsPerRow = Math.max(minCardsPerRow, maxCardsPerRow);

        // If we have very little width, reduce card spacing
        if (gridAvailableWidth < 400) {
            // For small screens, pack cards tighter
            cardsPerRow = Math.max(1, gridAvailableWidth / (CARD_WIDTH + 4)); // Reduced spacing
        }

        int gridAvailableHeight = panelHeight - HEADER_HEIGHT - TAB_HEIGHT - SPACING_SM * 3;
        visibleRows = Math.max(1, gridAvailableHeight / (CARD_HEIGHT + CARD_SPACING)); // Min 1 row visible

        // Apply small screen optimizations if needed
        applySmallScreenOptimizations();

        Pokecobbleclaim.LOGGER.debug("Panel size: {}x{}, Cards per row: {}, Visible rows: {}, Screen: {}x{}",
            panelWidth, panelHeight, cardsPerRow, visibleRows, width, height);
    }

    /**
     * Applies optimizations for very small screens.
     */
    private void applySmallScreenOptimizations() {
        // For very small screens (like mobile or small windows)
        if (width < 480 || height < 360) {
            // Force single column layout for very narrow screens
            if (width < 320) {
                cardsPerRow = 1;
            }

            // Reduce visible rows requirement for very short screens
            if (height < 300) {
                visibleRows = Math.max(1, visibleRows);
            }

            // Handle extremely small screens
            if (width < 250 || height < 200) {
                Pokecobbleclaim.LOGGER.warn("Screen size {}x{} is very small, UI may be cramped", width, height);
                // Force minimal layout
                cardsPerRow = 1;
                visibleRows = 1;
            }

            Pokecobbleclaim.LOGGER.debug("Applied small screen optimizations for {}x{}", width, height);
        }
    }

    /**
     * Gets responsive spacing based on screen size.
     */
    private int getResponsiveSpacing(int baseSpacing) {
        if (width < 400 || height < 300) {
            return Math.max(2, baseSpacing / 2); // Half spacing for very small screens
        } else if (width < 600 || height < 400) {
            return Math.max(4, (baseSpacing * 3) / 4); // 75% spacing for small screens
        }
        return baseSpacing; // Full spacing for normal screens
    }

    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        // Render modern background
        this.renderBackground(context);

        // Calculate panel position - ensure it always fits on screen
        int leftX = Math.max(10, Math.min((width - panelWidth) / 2, width - panelWidth - 10));
        int topY = Math.max(10, Math.min((height - panelHeight) / 2, height - panelHeight - 10));

        // Draw main glass panel
        drawGlassPanel(context, leftX, topY, panelWidth, panelHeight);

        // Draw shop header with title and search
        drawShopHeader(context, leftX, topY, panelWidth, mouseX, mouseY);

        // Draw edit mode indicator if enabled
        if (isEditModeEnabled()) {
            drawEditModeIndicator(context, leftX, topY, panelWidth);
        }

        // Draw category tabs
        drawCategoryTabs(context, leftX, topY + HEADER_HEIGHT, panelWidth, mouseX, mouseY);

        // Draw price update timer (if in food category)
        int timerHeight = 0;
        if (selectedCategory != null && selectedCategory.getId().equals("food")) {
            timerHeight = 16; // Height for timer display
            drawPriceUpdateTimer(context, leftX, topY + HEADER_HEIGHT + TAB_HEIGHT, panelWidth);
        }

        // Calculate product grid area with proper margins
        int gridX = leftX + SPACING_SM;
        int gridY = topY + HEADER_HEIGHT + TAB_HEIGHT + SPACING_XS + timerHeight;
        int gridWidth = panelWidth - SPACING_SM * 2;
        int gridHeight = panelHeight - HEADER_HEIGHT - TAB_HEIGHT - SPACING_SM - SPACING_XS - timerHeight;

        // Draw product grid with scissor for proper clipping
        drawProductGrid(context, gridX, gridY, gridWidth, gridHeight, mouseX, mouseY);

        super.render(context, mouseX, mouseY, delta);
    }

    private void drawGlassPanel(DrawContext context, int x, int y, int width, int height) {
        // Drop shadow for depth
        context.fill(x + 2, y + 2, x + width + 2, y + height + 2, 0x60000000);

        // Main panel background with professional gradient effect
        context.fill(x, y, x + width, y + height, PANEL_BACKGROUND);

        // Subtle inner gradient for depth
        context.fill(x, y, x + width, y + 1, 0x40FFFFFF); // Top highlight
        context.fill(x, y, x + 1, y + height, 0x20FFFFFF); // Left highlight
        context.fill(x, y + height - 1, x + width, y + height, 0x60000000); // Bottom shadow
        context.fill(x + width - 1, y, x + width, y + height, 0x40000000); // Right shadow

        // Professional border
        context.drawBorder(x, y, width, height, BORDER_COLOR);

        // Inner border for premium look
        context.drawBorder(x + 1, y + 1, width - 2, height - 2, 0x20FFFFFF);
    }

    private void drawShopHeader(DrawContext context, int x, int y, int width, int mouseX, int mouseY) {
        // Professional header background with gradient
        int headerX = x + SPACING_XS;
        int headerY = y + SPACING_XS;
        int headerWidth = width - SPACING_XS * 2;
        int headerHeight = HEADER_HEIGHT - SPACING_XS;

        // Header background with subtle gradient
        context.fill(headerX, headerY, headerX + headerWidth, headerY + headerHeight, GLASS_CONTENT_BG);
        context.fill(headerX, headerY, headerX + headerWidth, headerY + 1, 0x30FFFFFF); // Top highlight
        context.fill(headerX, headerY + headerHeight - 1, headerX + headerWidth, headerY + headerHeight, 0x30000000); // Bottom shadow
        context.drawBorder(headerX, headerY, headerWidth, headerHeight, BORDER_COLOR);

        // Professional shop title with icon
        String title = "🌾 Farmer's Market";
        context.drawTextWithShadow(this.textRenderer, title, x + SPACING_SM + 2, y + SPACING_SM + 2, TEXT_PRIMARY);

        // Professional subtitle
        String subtitle = "Premium Agricultural Exchange";
        context.drawTextWithShadow(this.textRenderer, subtitle, x + SPACING_SM + 2, y + SPACING_SM + 14, TEXT_SECONDARY);

        // Professional search bar
        int searchWidth = Math.min(160, width - 220);
        int searchX = x + width - searchWidth - SPACING_SM - 2;
        int searchY = y + SPACING_SM + 2;
        boolean searchHovered = mouseX >= searchX && mouseX <= searchX + searchWidth &&
                               mouseY >= searchY && mouseY <= searchY + SEARCH_HEIGHT;

        // Search bar with professional styling
        int searchBg = searchHovered ? 0xF0252525 : 0xF01E1E1E;
        int searchBorder = searchHovered ? BORDER_ACCENT : BORDER_COLOR;

        context.fill(searchX, searchY, searchX + searchWidth, searchY + SEARCH_HEIGHT, searchBg);
        context.drawBorder(searchX, searchY, searchWidth, SEARCH_HEIGHT, searchBorder);

        String searchText = searchQuery.isEmpty() ? "🔍 Search products..." : "🔍 " + searchQuery;
        int searchTextColor = searchQuery.isEmpty() ? TEXT_SECONDARY : TEXT_PRIMARY;

        // Trim search text if too long
        if (this.textRenderer.getWidth(searchText) > searchWidth - SPACING_XS * 2) {
            searchText = this.textRenderer.trimToWidth(searchText, searchWidth - SPACING_XS * 2 - 10) + "...";
        }
        context.drawTextWithShadow(this.textRenderer, searchText, searchX + SPACING_XS, searchY + 3, searchTextColor);

        // Professional balance display with background
        String balanceLabel = "Balance:";
        String balanceAmount = getFormattedBalance();
        int balanceX = x + width - 120;
        int balanceY = y + HEADER_HEIGHT - 18;

        // Balance background
        context.fill(balanceX - 4, balanceY - 2, balanceX + 116, balanceY + 12, 0x80000000);
        context.drawBorder(balanceX - 4, balanceY - 2, 120, 14, BORDER_COLOR);

        context.drawTextWithShadow(this.textRenderer, balanceLabel, balanceX, balanceY, TEXT_SECONDARY);
        context.drawTextWithShadow(this.textRenderer, balanceAmount, balanceX + 45, balanceY, TEXT_PRICE);
    }

    private void drawCategoryTabs(DrawContext context, int x, int y, int width, int mouseX, int mouseY) {
        if (categories.isEmpty()) return;

        int availableWidth = width - SPACING_SM * 2;
        int tabWidth = availableWidth / categories.size();
        int currentX = x + SPACING_SM;

        for (ShopCategory category : categories) {
            boolean isSelected = category == selectedCategory;
            boolean isHovered = mouseX >= currentX && mouseX <= currentX + tabWidth &&
                               mouseY >= y && mouseY <= y + TAB_HEIGHT;

            // Professional tab styling
            int tabColor = isSelected ? TAB_SELECTED : TAB_UNSELECTED;
            int borderColor = isSelected ? BORDER_ACCENT : BORDER_COLOR;

            if (isHovered && !isSelected) {
                tabColor = TAB_HOVER;
                borderColor = BORDER_ACCENT;
            }

            // Tab background with professional styling
            int tabX = currentX;
            int tabW = tabWidth - TAB_SPACING;

            context.fill(tabX, y, tabX + tabW, y + TAB_HEIGHT, tabColor);

            // Professional tab borders
            if (isSelected) {
                // Selected tab gets accent border and highlight
                context.fill(tabX, y, tabX + tabW, y + 1, BORDER_ACCENT); // Top accent
                context.fill(tabX, y, tabX + 1, y + TAB_HEIGHT, 0x40FFFFFF); // Left highlight
                context.fill(tabX + tabW - 1, y, tabX + tabW, y + TAB_HEIGHT, 0x40000000); // Right shadow
            } else {
                // Unselected tabs get subtle borders
                context.fill(tabX, y + TAB_HEIGHT - 1, tabX + tabW, y + TAB_HEIGHT, borderColor);
            }

            // Tab text with professional styling
            String tabText = category.getName();
            int maxTextWidth = tabW - SPACING_XS * 2;
            if (this.textRenderer.getWidth(tabText) > maxTextWidth) {
                tabText = this.textRenderer.trimToWidth(tabText, maxTextWidth - 10) + "...";
            }

            int textWidth = this.textRenderer.getWidth(tabText);
            int textX = tabX + (tabW - textWidth) / 2;
            int textY = y + (TAB_HEIGHT - 8) / 2;
            int textColor = isSelected ? TEXT_PRIMARY : TEXT_SECONDARY;

            context.drawTextWithShadow(this.textRenderer, tabText, textX, textY, textColor);

            // Update category position for click detection
            category.setPosition(currentX, y, tabW, TAB_HEIGHT);
            currentX += tabWidth;
        }
    }

    private void drawProductGrid(DrawContext context, int x, int y, int width, int height, int mouseX, int mouseY) {
        // Enable scissor to clip content to the grid area
        context.enableScissor(x, y, x + width, y + height);

        try {
            // Special handling for statistics category
            if (selectedCategory != null && selectedCategory.getId().equals("stats")) {
                drawStatistics(context, x, y, width, height);
                return;
            }

            // Special handling for upgrades category (with sections)
            if (selectedCategory != null && selectedCategory.getId().equals("upgrades")) {
                drawUpgradesWithSections(context, x, y, width, height, mouseX, mouseY);
                return;
            }

            if (filteredProducts.isEmpty()) {
                // Professional empty state
                drawProfessionalEmptyState(context, x, y, width, height);
                return;
            }

            // Calculate grid layout with proper spacing
            int actualCardWidth = CARD_WIDTH;
            int actualCardSpacing = CARD_SPACING;

            // Adjust card size if needed to fit better
            cardsPerRow = Math.max(1, Math.min(5, (width + actualCardSpacing) / (actualCardWidth + actualCardSpacing)));

            // Recalculate spacing to center cards
            int totalCardsWidth = cardsPerRow * actualCardWidth + (cardsPerRow - 1) * actualCardSpacing;
            int startX = x + (width - totalCardsWidth) / 2;

            // Calculate total items including "Add New Product" card when in edit mode
            int totalItems = filteredProducts.size();
            if (isEditModeEnabled()) {
                totalItems += 1; // Add one for the "Add New Product" card
            }
            int totalRows = (totalItems + cardsPerRow - 1) / cardsPerRow;
            int maxScrollOffset = calculateProductsMaxScroll(height);
            scrollOffset = Math.min(scrollOffset, maxScrollOffset);

            // Draw product cards with proper clipping
            int cardIndex = 0;
            int currentY = y - scrollOffset;

            // Calculate row height including space for modify button when in edit mode
            int buttonSpace = isEditModeEnabled() ? 22 : 0; // 18px button + 2px gap + 2px margin
            int rowHeight = CARD_HEIGHT + buttonSpace + actualCardSpacing;

            for (int row = 0; row < totalRows; row++) {
                int rowY = currentY + row * rowHeight;

                // Skip rows that are completely above the visible area
                if (rowY + CARD_HEIGHT < y) {
                    cardIndex += Math.min(cardsPerRow, filteredProducts.size() - cardIndex);
                    continue;
                }

                // Stop drawing rows that are completely below the visible area
                if (rowY > y + height) {
                    break;
                }

                for (int col = 0; col < cardsPerRow; col++) {
                    int cardX = startX + col * (actualCardWidth + actualCardSpacing);

                    // Check if we should draw a product card or the "Add New" card
                    if (cardIndex < filteredProducts.size()) {
                        // Draw regular product card
                        Product product = filteredProducts.get(cardIndex);
                        drawProductCard(context, product, cardX, rowY, mouseX, mouseY);

                        // Draw modify button under the card if in edit mode
                        if (isEditModeEnabled()) {
                            boolean isCardHovered = mouseX >= cardX && mouseX <= cardX + CARD_WIDTH &&
                                                   mouseY >= rowY && mouseY <= rowY + CARD_HEIGHT;
                            drawModifyButton(context, cardX, rowY, mouseX, mouseY, isCardHovered);
                        }
                        cardIndex++;
                    } else if (isEditModeEnabled() && cardIndex == filteredProducts.size()) {
                        // Draw "Add New Product" card as the first card after all products
                        drawAddNewProductCard(context, cardX, rowY, mouseX, mouseY);
                        cardIndex++; // Increment to prevent drawing multiple add cards
                    } else {
                        // No more cards to draw
                        break;
                    }
                }
            }
        } finally {
            // Always disable scissor when done
            context.disableScissor();
        }

        // Draw scroll indicator outside the scissor area
        drawScrollIndicator(context, x + width - 6, y, height);
    }

    /**
     * Draws the upgrades category with separate sections for upgrades and unlock tools.
     */
    private void drawUpgradesWithSections(DrawContext context, int x, int y, int width, int height, int mouseX, int mouseY) {
        if (filteredProducts.isEmpty()) {
            drawProfessionalEmptyState(context, x, y, width, height);
            return;
        }

        // Separate products into upgrades and unlocks
        List<Product> upgrades = new ArrayList<>();
        List<Product> unlocks = new ArrayList<>();

        for (Product product : filteredProducts) {
            if (product.getType() == ProductType.UPGRADE) {
                upgrades.add(product);
            } else if (product.getType() == ProductType.UNLOCK) {
                unlocks.add(product);
            }
        }

        int currentY = y - scrollOffset;
        int sectionSpacing = SPACING_MD;

        // Draw Upgrades section
        if (!upgrades.isEmpty()) {
            // Section header
            String upgradesTitle = "🔧 Farming Upgrades";
            context.drawTextWithShadow(this.textRenderer, upgradesTitle, x + SPACING_SM, currentY, TEXT_ACCENT);
            currentY += 16; // Header height

            // Draw upgrades grid
            int upgradesHeight = drawProductSection(context, x, currentY, width, upgrades, mouseX, mouseY);
            currentY += upgradesHeight + sectionSpacing;
        }

        // Draw Unlock Tools section
        if (!unlocks.isEmpty()) {
            // Section header
            String unlocksTitle = "🔓 Unlock New Tools";
            context.drawTextWithShadow(this.textRenderer, unlocksTitle, x + SPACING_SM, currentY, TEXT_ACCENT);
            currentY += 16; // Header height

            // Draw unlocks grid
            drawProductSection(context, x, currentY, width, unlocks, mouseX, mouseY);
        }
    }

    /**
     * Draws a section of products and returns the height used.
     */
    private int drawProductSection(DrawContext context, int x, int y, int width, List<Product> products, int mouseX, int mouseY) {
        if (products.isEmpty()) {
            return 0;
        }

        // Calculate grid layout
        int actualCardWidth = CARD_WIDTH;
        int actualCardSpacing = CARD_SPACING;
        int cardsPerRow = Math.max(1, (width - SPACING_SM * 2) / (actualCardWidth + actualCardSpacing));

        int currentX = x + SPACING_SM;
        int currentY = y;
        // Calculate row height including space for modify button when in edit mode
        int buttonSpace = isEditModeEnabled() ? 22 : 0; // 18px button + 2px gap + 2px margin
        int rowHeight = CARD_HEIGHT + buttonSpace + actualCardSpacing;
        int cardsInCurrentRow = 0;

        for (Product product : products) {
            // Check if card is visible (basic culling)
            if (currentY + CARD_HEIGHT < 0 || currentY > height + scrollOffset) {
                // Skip rendering but still update position
                cardsInCurrentRow++;
                if (cardsInCurrentRow >= cardsPerRow) {
                    cardsInCurrentRow = 0;
                    currentX = x + SPACING_SM;
                    currentY += rowHeight;
                } else {
                    currentX += actualCardWidth + actualCardSpacing;
                }
                continue;
            }

            // Draw the product card
            drawProductCard(context, product, currentX, currentY, mouseX, mouseY);

            // Draw modify button under the card if in edit mode
            if (isEditModeEnabled()) {
                boolean isCardHovered = mouseX >= currentX && mouseX <= currentX + CARD_WIDTH &&
                                       mouseY >= currentY && mouseY <= currentY + CARD_HEIGHT;
                drawModifyButton(context, currentX, currentY, mouseX, mouseY, isCardHovered);
            }

            // Update position for next card
            cardsInCurrentRow++;
            if (cardsInCurrentRow >= cardsPerRow) {
                cardsInCurrentRow = 0;
                currentX = x + SPACING_SM;
                currentY += rowHeight;
            } else {
                currentX += actualCardWidth + actualCardSpacing;
            }
        }

        // Calculate total height used
        int totalRows = (products.size() + cardsPerRow - 1) / cardsPerRow;
        return totalRows * rowHeight;
    }

    private void drawStatistics(DrawContext context, int x, int y, int width, int height) {
        // Professional statistics display with proper scrolling
        int cardHeight = 50; // Match the height used in calculateStatsMaxScroll
        int cardSpacing = SPACING_SM;
        int cardY = y + SPACING_SM - scrollOffset;

        // Professional Farming Level Card
        if (cardY + cardHeight >= y && cardY <= y + height) {
            drawProfessionalStatCard(context, x, cardY, width, cardHeight,
                "🌾", "Farming Level", "Level 7", "2,450 / 3,000 XP", TEXT_PRICE);
        }

        cardY += cardHeight + cardSpacing;

        // Professional Crops Harvested Card
        if (cardY + cardHeight >= y && cardY <= y + height) {
            drawProfessionalStatCard(context, x, cardY, width, cardHeight,
                "🥕", "Crops Harvested", "1,247", "This week: +89", TEXT_PRICE);
        }

        cardY += cardHeight + cardSpacing;

        // Professional Coins Earned Card
        if (cardY + cardHeight >= y && cardY <= y + height) {
            drawProfessionalStatCard(context, x, cardY, width, cardHeight,
                "💰", "Revenue Generated", "8,932 💰", "Today: +156 💰", TEXT_PRICE);
        }

        cardY += cardHeight + cardSpacing;

        // Professional Achievement Card
        if (cardY + cardHeight >= y && cardY <= y + height) {
            drawProfessionalStatCard(context, x, cardY, width, cardHeight,
                "🏆", "Achievements", "12 / 25", "Next: Master Farmer", TEXT_DISCOUNT);
        }
    }

    private void drawProfessionalStatCard(DrawContext context, int x, int y, int width, int height,
                                        String icon, String title, String value, String subtitle, int accentColor) {
        // Professional card background with gradient
        context.fill(x, y, x + width, y + height, CARD_BG);

        // Card highlights and shadows
        context.fill(x, y, x + width, y + 1, 0x30FFFFFF); // Top highlight
        context.fill(x, y, x + 1, y + height, 0x20FFFFFF); // Left highlight
        context.fill(x, y + height - 1, x + width, y + height, 0x30000000); // Bottom shadow
        context.fill(x + width - 1, y, x + width, y + height, 0x20000000); // Right shadow

        // Professional border
        context.drawBorder(x, y, width, height, CARD_BORDER);

        // Icon background circle
        int iconSize = 24;
        int iconX = x + SPACING_SM;
        int iconY = y + (height - iconSize) / 2;
        context.fill(iconX - 2, iconY - 2, iconX + iconSize + 2, iconY + iconSize + 2, 0x40000000);
        context.fill(iconX, iconY, iconX + iconSize, iconY + iconSize, 0x20FFFFFF);

        // Icon
        context.drawTextWithShadow(this.textRenderer, icon, iconX + 4, iconY + 8, TEXT_PRIMARY);

        // Title and content
        int textX = x + SPACING_SM + iconSize + SPACING_SM;
        context.drawTextWithShadow(this.textRenderer, title, textX, y + SPACING_XS + 2, TEXT_PRIMARY);
        context.drawTextWithShadow(this.textRenderer, value, textX, y + SPACING_XS + 14, accentColor);
        context.drawTextWithShadow(this.textRenderer, subtitle, textX, y + height - 14, TEXT_SECONDARY);

        // Progress indicator (optional visual element)
        int progressWidth = 40;
        int progressX = x + width - progressWidth - SPACING_SM;
        int progressY = y + height - 8;
        context.fill(progressX, progressY, progressX + progressWidth, progressY + 2, 0x40FFFFFF);
        context.fill(progressX, progressY, progressX + (progressWidth * 3 / 4), progressY + 2, accentColor);
    }

    private void drawProfessionalEmptyState(DrawContext context, int x, int y, int width, int height) {
        // Professional empty state with icon and message
        int centerX = x + width / 2;
        int centerY = y + height / 2;

        // Empty state icon background
        int iconSize = 48;
        int iconX = centerX - iconSize / 2;
        int iconY = centerY - iconSize / 2 - 20;

        context.fill(iconX, iconY, iconX + iconSize, iconY + iconSize, 0x40404040);
        context.drawBorder(iconX, iconY, iconSize, iconSize, BORDER_COLOR);

        // Empty state icon
        String emptyIcon = searchQuery.isEmpty() ? "📦" : "🔍";
        context.drawTextWithShadow(this.textRenderer, emptyIcon, iconX + 16, iconY + 20, TEXT_SECONDARY);

        // Empty state title
        String emptyTitle = searchQuery.isEmpty() ? "No Products Available" : "No Results Found";
        int titleWidth = this.textRenderer.getWidth(emptyTitle);
        context.drawTextWithShadow(this.textRenderer, emptyTitle, centerX - titleWidth / 2, centerY + 10, TEXT_PRIMARY);

        // Empty state subtitle
        String emptySubtitle = searchQuery.isEmpty() ?
            "Products will appear here when available" :
            "Try adjusting your search terms";
        int subtitleWidth = this.textRenderer.getWidth(emptySubtitle);
        context.drawTextWithShadow(this.textRenderer, emptySubtitle, centerX - subtitleWidth / 2, centerY + 25, TEXT_SECONDARY);

        // Professional suggestion
        if (!searchQuery.isEmpty()) {
            String suggestion = "Search for: seeds, food, tools";
            int suggestionWidth = this.textRenderer.getWidth(suggestion);
            context.drawTextWithShadow(this.textRenderer, suggestion, centerX - suggestionWidth / 2, centerY + 40, TEXT_MUTED);
        }
    }

    /**
     * Checks if edit mode is enabled for the current player.
     */
    private boolean isEditModeEnabled() {
        boolean enabled = FarmerEditModeManager.getInstance().isEditModeEnabled();
        // Debug logging (remove in production)
        if (enabled) {
            Pokecobbleclaim.LOGGER.debug("Edit mode is enabled - modify buttons should be visible");
        }
        return enabled;
    }

    /**
     * Draws an edit mode indicator at the top of the screen.
     */
    private void drawEditModeIndicator(DrawContext context, int x, int y, int width) {
        // Draw edit mode indicator in top-right corner
        String editText = "✏ EDIT MODE";
        int textWidth = this.textRenderer.getWidth(editText);
        int indicatorWidth = textWidth + 12;
        int indicatorHeight = 16;
        int indicatorX = x + width - indicatorWidth - 8;
        int indicatorY = y + 6;

        // Draw background with glass effect
        context.fill(indicatorX, indicatorY, indicatorX + indicatorWidth, indicatorY + indicatorHeight, 0x804CAF50);
        context.fill(indicatorX + 1, indicatorY + 1, indicatorX + indicatorWidth - 1, indicatorY + 2, 0x40FFFFFF);
        context.drawBorder(indicatorX, indicatorY, indicatorWidth, indicatorHeight, 0xFF4CAF50);

        // Draw text
        int textX = indicatorX + (indicatorWidth - textWidth) / 2;
        int textY = indicatorY + (indicatorHeight - 8) / 2;
        context.drawTextWithShadow(this.textRenderer, editText, textX, textY, 0xFFFFFFFF);
    }

    private void drawProductCard(DrawContext context, Product product, int x, int y, int mouseX, int mouseY) {
        boolean isHovered = mouseX >= x && mouseX <= x + CARD_WIDTH &&
                           mouseY >= y && mouseY <= y + CARD_HEIGHT;

        // Professional card with shadow and gradient
        if (isHovered) {
            // Drop shadow for hovered cards
            context.fill(x + 1, y + 1, x + CARD_WIDTH + 1, y + CARD_HEIGHT + 1, 0x80000000);
        }

        // Card background with professional styling
        int cardBg = isHovered ? CARD_HOVER_BG : CARD_BG;
        context.fill(x, y, x + CARD_WIDTH, y + CARD_HEIGHT, cardBg);

        // Professional card borders
        int borderColor = isHovered ? BORDER_ACCENT : CARD_BORDER;
        context.drawBorder(x, y, CARD_WIDTH, CARD_HEIGHT, borderColor);

        // Subtle inner highlight for premium look
        context.fill(x + 1, y + 1, x + CARD_WIDTH - 1, y + 2, 0x20FFFFFF);
        context.fill(x + 1, y + 1, x + 2, y + CARD_HEIGHT - 1, 0x10FFFFFF);

        // Product icon - use ItemStack for seeds/food, texture for tools
        int iconX = x + (CARD_WIDTH - ICON_SIZE) / 2;
        int iconY = y + SPACING_XS + 2; // Reduced spacing for more compact layout

        if (product.hasItemStack()) {
            // Render Minecraft item
            try {
                context.drawItem(product.getItemStack(), iconX, iconY);
                // Draw item count overlay if needed (optional)
                // context.drawItemInSlot(this.textRenderer, product.getItemStack(), iconX, iconY);
            } catch (Exception e) {
                // Fallback to colored rectangle with type icon if item rendering fails
                context.fill(iconX, iconY, iconX + ICON_SIZE, iconY + ICON_SIZE, getProductTypeColor(product.getType()));
                String typeIcon = getProductTypeIcon(product.getType());
                context.drawTextWithShadow(this.textRenderer, typeIcon, iconX + 2, iconY + 2, TEXT_PRIMARY);
            }
        } else if (product.getIconPath() != null) {
            // Render texture for tools
            try {
                Identifier textureId = new Identifier(product.getIconPath());
                context.drawTexture(textureId, iconX, iconY, 0, 0, ICON_SIZE, ICON_SIZE, ICON_SIZE, ICON_SIZE);
            } catch (Exception e) {
                // Fallback to colored rectangle with type icon if texture fails
                context.fill(iconX, iconY, iconX + ICON_SIZE, iconY + ICON_SIZE, getProductTypeColor(product.getType()));
                String typeIcon = getProductTypeIcon(product.getType());
                context.drawTextWithShadow(this.textRenderer, typeIcon, iconX + 2, iconY + 2, TEXT_PRIMARY);
            }
        } else {
            // Fallback for products without icon
            context.fill(iconX, iconY, iconX + ICON_SIZE, iconY + ICON_SIZE, getProductTypeColor(product.getType()));
            String typeIcon = getProductTypeIcon(product.getType());
            context.drawTextWithShadow(this.textRenderer, typeIcon, iconX + 2, iconY + 2, TEXT_PRIMARY);
        }

        // Product name - more compact
        String name = product.getName();
        int maxNameWidth = CARD_WIDTH - SPACING_XS * 2;
        if (this.textRenderer.getWidth(name) > maxNameWidth) {
            name = this.textRenderer.trimToWidth(name, maxNameWidth - 8) + "...";
        }
        int nameX = x + (CARD_WIDTH - this.textRenderer.getWidth(name)) / 2;
        context.drawTextWithShadow(this.textRenderer, name, nameX, y + ICON_SIZE + SPACING_XS, TEXT_PRIMARY);

        // Professional price display with dynamic pricing (adjusted for smaller card)
        int priceY = y + CARD_HEIGHT - 16; // Reduced from 20 to 16 for even smaller card
        int currentPrice = getCurrentDynamicPrice(product);

        if (product.isOnSale()) {
            // Professional sale pricing layout
            String originalPrice = currentPrice + "💰";
            String salePrice = product.getDiscountedPrice() + "💰";

            // Strike-through effect for original price
            int originalPriceWidth = this.textRenderer.getWidth(originalPrice);
            context.drawTextWithShadow(this.textRenderer, originalPrice, x + SPACING_XS, priceY, 0xFF888888);
            context.fill(x + SPACING_XS, priceY + 4, x + SPACING_XS + originalPriceWidth, priceY + 5, 0xFF888888);

            // Sale price with emphasis
            context.drawTextWithShadow(this.textRenderer, salePrice, x + SPACING_XS, priceY + 10, TEXT_PRICE);

            // Professional discount badge
            String discount = "-" + product.getDiscountPercent() + "%";
            int badgeWidth = Math.max(24, this.textRenderer.getWidth(discount) + 4);
            int badgeHeight = 10;
            int badgeX = x + CARD_WIDTH - badgeWidth - SPACING_XS;
            int badgeY = y + SPACING_XS;

            // Badge background with gradient
            context.fill(badgeX, badgeY, badgeX + badgeWidth, badgeY + badgeHeight, SALE_BADGE_BG);
            context.fill(badgeX, badgeY, badgeX + badgeWidth, badgeY + 1, 0x40FFFFFF); // Top highlight
            context.drawBorder(badgeX, badgeY, badgeWidth, badgeHeight, 0xFFCC6600);

            // Badge text
            int discountTextX = badgeX + (badgeWidth - this.textRenderer.getWidth(discount)) / 2;
            context.drawTextWithShadow(this.textRenderer, discount, discountTextX, badgeY + 1, TEXT_PRIMARY);
        } else {
            // Regular price with professional styling (using dynamic price)
            String price = currentPrice + "💰";

            // Show price change indicator for food items
            if (product.getType() == ProductType.FOOD && currentPrice != product.getPrice()) {
                int staticPrice = product.getPrice();
                boolean priceUp = currentPrice > staticPrice;

                // Calculate percentage change
                double changePercent = ((double)(currentPrice - staticPrice) / staticPrice) * 100;

                // Format percentage - show 1 decimal for small changes, whole numbers for large changes
                String percentText;
                if (Math.abs(changePercent) < 10) {
                    percentText = String.format("%.1f%%", Math.abs(changePercent));
                } else {
                    percentText = String.format("%.0f%%", Math.abs(changePercent));
                }

                String arrow = priceUp ? "↗" : "↘";
                String indicator = " " + arrow + percentText;
                int indicatorColor = priceUp ? 0xFF00DD00 : 0xFFDD4444; // Slightly darker colors for better readability

                // Draw price and indicator, ensuring they fit within the card
                int priceWidth = this.textRenderer.getWidth(price);
                int indicatorWidth = this.textRenderer.getWidth(indicator);
                int totalWidth = priceWidth + indicatorWidth;
                int availableWidth = CARD_WIDTH - SPACING_XS * 2;

                if (totalWidth <= availableWidth) {
                    // Normal display - both price and indicator fit
                    context.drawTextWithShadow(this.textRenderer, price, x + SPACING_XS, priceY + 5, TEXT_PRICE);
                    context.drawTextWithShadow(this.textRenderer, indicator,
                        x + SPACING_XS + priceWidth, priceY + 5, indicatorColor);
                } else {
                    // Compact display - center the price and put indicator below
                    int priceX = x + (CARD_WIDTH - priceWidth) / 2;
                    context.drawTextWithShadow(this.textRenderer, price, priceX, priceY + 2, TEXT_PRICE);

                    int indicatorX = x + (CARD_WIDTH - indicatorWidth) / 2;
                    context.drawTextWithShadow(this.textRenderer, indicator,
                        indicatorX, priceY + 12, indicatorColor);
                }
            } else {
                // Center the price when there's no indicator
                int priceWidth = this.textRenderer.getWidth(price);
                int priceX = x + (CARD_WIDTH - priceWidth) / 2;
                context.drawTextWithShadow(this.textRenderer, price, priceX, priceY + 5, TEXT_PRICE);
            }
        }

        // Gamification elements for upgrades and unlocks
        if (product.getType() == ProductType.UPGRADE) {
            drawUpgradeCard(context, x, y, product, isHovered);
        } else if (product.getType() == ProductType.UNLOCK) {
            drawUnlockCard(context, x, y, product, isHovered);
        } else {
            // Professional level requirement badge for regular items
            if (product.getRequiredLevel() > 0) {
                String levelReq = "Lv." + product.getRequiredLevel();
                int levelWidth = this.textRenderer.getWidth(levelReq);
                int levelBadgeWidth = levelWidth + 6;
                int levelBadgeX = x + CARD_WIDTH - levelBadgeWidth - SPACING_XS;
                int levelBadgeY = y + CARD_HEIGHT - 10; // Adjusted for even smaller card

                // Level badge background
                context.fill(levelBadgeX, levelBadgeY, levelBadgeX + levelBadgeWidth, levelBadgeY + 10, LEVEL_REQ_BG);
                context.drawBorder(levelBadgeX, levelBadgeY, levelBadgeWidth, 10, BORDER_COLOR);

                // Level text
                context.drawTextWithShadow(this.textRenderer, levelReq, levelBadgeX + 3, levelBadgeY + 1, TEXT_SECONDARY);
            }
        }

        // Draw edit mode overlay (without button - button will be drawn separately below card)
        if (isEditModeEnabled()) {
            // Draw subtle edit mode overlay on the entire card
            context.fill(x, y, x + CARD_WIDTH, y + CARD_HEIGHT, 0x204CAF50);
        }
    }

    /**
     * Draws an "Add New Product" card when in edit mode.
     */
    private void drawAddNewProductCard(DrawContext context, int x, int y, int mouseX, int mouseY) {
        // Check if mouse is hovering over the add card
        boolean isHovered = mouseX >= x && mouseX <= x + CARD_WIDTH &&
                           mouseY >= y && mouseY <= y + CARD_HEIGHT;

        // Card background with dashed border style
        int backgroundColor = isHovered ? 0xFF2A2A2A : 0xFF1E1E1E;
        int borderColor = isHovered ? 0xFF4CAF50 : 0xFF666666;

        // Draw card background
        context.fill(x, y, x + CARD_WIDTH, y + CARD_HEIGHT, backgroundColor);

        // Draw dashed border effect
        drawDashedBorder(context, x, y, CARD_WIDTH, CARD_HEIGHT, borderColor);

        // Draw large + icon in center
        int centerX = x + CARD_WIDTH / 2;
        int centerY = y + CARD_HEIGHT / 2;
        int iconSize = 24;
        int iconColor = isHovered ? 0xFF4CAF50 : 0xFF888888;

        // Draw + icon (horizontal and vertical lines)
        int lineThickness = 3;
        // Horizontal line
        context.fill(centerX - iconSize/2, centerY - lineThickness/2,
                    centerX + iconSize/2, centerY + lineThickness/2 + 1, iconColor);
        // Vertical line
        context.fill(centerX - lineThickness/2, centerY - iconSize/2,
                    centerX + lineThickness/2 + 1, centerY + iconSize/2, iconColor);

        // Draw "Add New" text below icon
        String addText = "Add New";
        int textWidth = this.textRenderer.getWidth(addText);
        int textX = centerX - textWidth / 2;
        int textY = centerY + iconSize/2 + 8;
        int textColor = isHovered ? 0xFF4CAF50 : 0xFF888888;

        context.drawTextWithShadow(this.textRenderer, addText, textX, textY, textColor);

        // Draw product type text
        String typeText = "Product";
        int typeWidth = this.textRenderer.getWidth(typeText);
        int typeX = centerX - typeWidth / 2;
        int typeY = textY + 12;

        context.drawTextWithShadow(this.textRenderer, typeText, typeX, typeY, textColor);
    }

    /**
     * Draws a dashed border around a rectangle.
     */
    private void drawDashedBorder(DrawContext context, int x, int y, int width, int height, int color) {
        int dashLength = 4;
        int gapLength = 3;

        // Top border
        for (int i = 0; i < width; i += dashLength + gapLength) {
            int endX = Math.min(i + dashLength, width);
            context.fill(x + i, y, x + endX, y + 1, color);
        }

        // Bottom border
        for (int i = 0; i < width; i += dashLength + gapLength) {
            int endX = Math.min(i + dashLength, width);
            context.fill(x + i, y + height - 1, x + endX, y + height, color);
        }

        // Left border
        for (int i = 0; i < height; i += dashLength + gapLength) {
            int endY = Math.min(i + dashLength, height);
            context.fill(x, y + i, x + 1, y + endY, color);
        }

        // Right border
        for (int i = 0; i < height; i += dashLength + gapLength) {
            int endY = Math.min(i + dashLength, height);
            context.fill(x + width - 1, y + i, x + width, y + endY, color);
        }
    }

    /**
     * Draws a "Modify" button under the card when in edit mode.
     */
    private void drawModifyButton(DrawContext context, int cardX, int cardY, int mouseX, int mouseY, boolean cardHovered) {
        // Button dimensions and position - positioned under the card
        int buttonWidth = CARD_WIDTH;  // Full width of card for better UX
        int buttonHeight = 18; // Slightly taller for better visibility
        int buttonX = cardX; // Align with card left edge
        int buttonY = cardY + CARD_HEIGHT + 2; // 2px gap below card

        // Check if mouse is over the modify button
        boolean isButtonHovered = mouseX >= buttonX && mouseX <= buttonX + buttonWidth &&
                                 mouseY >= buttonY && mouseY <= buttonY + buttonHeight;

        // Enhanced button colors - more prominent and always visible
        int buttonBg = isButtonHovered ? 0xFF81C784 : 0xFF4CAF50;      // Brighter green
        int buttonBorder = isButtonHovered ? 0xFF66BB6A : 0xFF388E3C;  // Darker border
        int shadowColor = isButtonHovered ? 0x80000000 : 0x60000000;   // Stronger shadow when hovered
        int textColor = 0xFFFFFFFF;

        // Draw enhanced button shadow for depth
        context.fill(buttonX + 2, buttonY + 2, buttonX + buttonWidth + 2, buttonY + buttonHeight + 2, shadowColor);

        // Draw button background with glass effect
        context.fill(buttonX, buttonY, buttonX + buttonWidth, buttonY + buttonHeight, buttonBg);

        // Add inner highlight for glass effect
        context.fill(buttonX + 1, buttonY + 1, buttonX + buttonWidth - 1, buttonY + 2, 0x40FFFFFF);
        context.fill(buttonX + 1, buttonY + 1, buttonX + 2, buttonY + buttonHeight - 1, 0x20FFFFFF);

        // Draw button border
        context.drawBorder(buttonX, buttonY, buttonWidth, buttonHeight, buttonBorder);

        // Draw button text with better positioning
        String buttonText = "✏ Edit";  // Changed text with icon
        int textWidth = this.textRenderer.getWidth(buttonText);
        int textX = buttonX + (buttonWidth - textWidth) / 2;
        int textY = buttonY + (buttonHeight - 8) / 2;

        context.drawTextWithShadow(this.textRenderer, buttonText, textX, textY, textColor);

        // Add subtle glow effect when hovered
        if (isButtonHovered) {
            context.drawBorder(buttonX - 1, buttonY - 1, buttonWidth + 2, buttonHeight + 2, 0x804CAF50);
        }
    }

    /**
     * Checks if the modify button was clicked.
     */
    private boolean isModifyButtonClicked(double mouseX, double mouseY, int cardX, int cardY) {
        // Button dimensions and position (same as in drawModifyButton) - under the card
        int buttonWidth = CARD_WIDTH;  // Full width of card
        int buttonHeight = 18; // Updated to match drawModifyButton
        int buttonX = cardX; // Align with card left edge
        int buttonY = cardY + CARD_HEIGHT + 2; // 2px gap below card

        boolean clicked = mouseX >= buttonX && mouseX <= buttonX + buttonWidth &&
                         mouseY >= buttonY && mouseY <= buttonY + buttonHeight;

        // Enhanced debug logging
        if (isEditModeEnabled()) {
            Pokecobbleclaim.LOGGER.debug("Modify button check: mouseX={}, mouseY={}, buttonX={}, buttonY={}, buttonW={}, buttonH={}, clicked={}",
                mouseX, mouseY, buttonX, buttonY, buttonWidth, buttonHeight, clicked);
        }

        if (clicked) {
            Pokecobbleclaim.LOGGER.info("✓ Modify button clicked! mouseX={}, mouseY={}, buttonX={}, buttonY={}, buttonW={}, buttonH={}",
                mouseX, mouseY, buttonX, buttonY, buttonWidth, buttonHeight);
        }

        return clicked;
    }



    /**
     * Opens the product edit screen for modifying product details.
     */
    private void openProductEditScreen(Product product) {
        if (this.client == null) {
            Pokecobbleclaim.LOGGER.error("Cannot open product edit screen: client is null");
            return;
        }

        Pokecobbleclaim.LOGGER.info("🔧 Opening ProductEditScreen for product: {}", product.getName());

        // Open the product edit screen
        ProductEditScreen editScreen = new ProductEditScreen(this, product);
        this.client.setScreen(editScreen);

        Pokecobbleclaim.LOGGER.info("✓ ProductEditScreen opened successfully");
    }

    /**
     * Opens the product creation screen for adding new products.
     */
    private void openProductCreationScreen() {
        Pokecobbleclaim.LOGGER.info("Opening product creation screen");

        if (this.client != null) {
            // Determine the current category type for the new product
            ProductType defaultType = ProductType.FOOD; // Default to food
            if (selectedCategory != null) {
                switch (selectedCategory.getId()) {
                    case "seeds":
                        defaultType = ProductType.SEED;
                        break;
                    case "food":
                        defaultType = ProductType.FOOD;
                        break;
                    case "tools":
                        defaultType = ProductType.TOOL;
                        break;
                    case "upgrades":
                        defaultType = ProductType.UPGRADE;
                        break;
                    case "unlocks":
                        defaultType = ProductType.UNLOCK;
                        break;
                }
            }

            // Open the product creation screen
            ProductCreateScreen createScreen = new ProductCreateScreen(this, defaultType);
            this.client.setScreen(createScreen);
        }
    }

    /**
     * Draws gamified upgrade card with progress indicators.
     */
    private void drawUpgradeCard(DrawContext context, int x, int y, Product product, boolean isHovered) {
        // Upgrade progress bar (simulate current progress)
        int currentLevel = getCurrentUpgradeLevel(product.getId());
        int maxLevel = 5; // Max upgrade level
        float progress = (float) currentLevel / maxLevel;

        // Progress bar at bottom of card
        int progressBarY = y + CARD_HEIGHT - 4;
        int progressBarWidth = CARD_WIDTH - SPACING_XS * 2;
        int progressBarX = x + SPACING_XS;
        int progressBarHeight = 3;

        // Progress bar background
        context.fill(progressBarX, progressBarY, progressBarX + progressBarWidth, progressBarY + progressBarHeight, 0x40000000);

        // Progress bar fill
        int fillWidth = (int) (progressBarWidth * progress);
        int progressColor = currentLevel >= maxLevel ? 0xFF00FF00 : 0xFFFF6B35; // Green if maxed, orange otherwise
        context.fill(progressBarX, progressBarY, progressBarX + fillWidth, progressBarY + progressBarHeight, progressColor);

        // Upgrade level indicator in top-right corner
        String levelText = currentLevel >= maxLevel ? "MAX" : currentLevel + "/" + maxLevel;
        int levelTextWidth = this.textRenderer.getWidth(levelText);
        int levelBadgeWidth = levelTextWidth + 6;
        int levelX = x + CARD_WIDTH - levelBadgeWidth - SPACING_XS;
        int levelY = y + SPACING_XS;

        // Level badge background
        context.fill(levelX, levelY, levelX + levelBadgeWidth, levelY + 10, 0x80000000);
        context.drawBorder(levelX, levelY, levelBadgeWidth, 10, 0xFF444444);

        // Level text
        int levelColor = currentLevel >= maxLevel ? 0xFF00FF00 : 0xFFFFFFFF;
        context.drawTextWithShadow(this.textRenderer, levelText, levelX + 3, levelY + 1, levelColor);

        // Upgrade effect text under name (only on hover and not maxed)
        if (isHovered && currentLevel < maxLevel) {
            String effectText = getUpgradeEffect(product.getId());
            int effectWidth = this.textRenderer.getWidth(effectText);
            int effectX = x + (CARD_WIDTH - effectWidth) / 2;
            int effectY = y + ICON_SIZE + SPACING_XS + 10; // Under the name

            // Effect background
            context.fill(effectX - 2, effectY - 1, effectX + effectWidth + 2, effectY + 9, 0xC0000000);

            // Effect text in orange
            context.drawTextWithShadow(this.textRenderer, effectText, effectX, effectY, 0xFFFFAA00);
        }
    }

    /**
     * Draws gamified unlock card with visual status indicators.
     */
    private void drawUnlockCard(DrawContext context, int x, int y, Product product, boolean isHovered) {
        boolean isUnlocked = isToolUnlocked(product.getId());
        boolean canUnlock = canUnlockTool(product.getId());

        // Visual status indicator in top-right corner (colored dot)
        int dotSize = 8;
        int dotX = x + CARD_WIDTH - dotSize - SPACING_XS;
        int dotY = y + SPACING_XS;

        int dotColor;
        if (isUnlocked) {
            dotColor = 0xFF00FF00; // Green for unlocked
        } else if (canUnlock) {
            dotColor = 0xFF35A7FF; // Blue for available
        } else {
            dotColor = 0xFF666666; // Gray for locked
        }

        // Draw status dot
        context.fill(dotX, dotY, dotX + dotSize, dotY + dotSize, dotColor);
        context.drawBorder(dotX, dotY, dotSize, dotSize, 0xFF000000);

        // Status indicator at bottom of card (colored bar)
        int statusBarY = y + CARD_HEIGHT - 3;
        int statusBarWidth = CARD_WIDTH - SPACING_XS * 2;
        int statusBarX = x + SPACING_XS;
        int statusBarHeight = 3;

        // Draw status bar
        context.fill(statusBarX, statusBarY, statusBarX + statusBarWidth, statusBarY + statusBarHeight, dotColor);

        // Requirement text under name (only on hover)
        if (isHovered && !isUnlocked) {
            String reqText = getUnlockRequirement(product.getId());
            int reqWidth = this.textRenderer.getWidth(reqText);
            int reqX = x + (CARD_WIDTH - reqWidth) / 2;
            int reqY = y + ICON_SIZE + SPACING_XS + 10; // Under the name

            // Requirement background
            context.fill(reqX - 2, reqY - 1, reqX + reqWidth + 2, reqY + 9, 0xC0000000);

            // Requirement text color based on availability
            int reqColor = canUnlock ? 0xFFAAFFAA : 0xFFFFAAAA; // Light green if available, light red if not
            context.drawTextWithShadow(this.textRenderer, reqText, reqX, reqY, reqColor);
        }

        // Unlock glow effect for available items
        if (canUnlock && !isUnlocked) {
            // Animated glow effect (simplified)
            int glowAlpha = (int) (64 + 32 * Math.sin(System.currentTimeMillis() * 0.005));
            int glowColor = (glowAlpha << 24) | 0x35A7FF;
            context.drawBorder(x - 1, y - 1, CARD_WIDTH + 2, CARD_HEIGHT + 2, glowColor);
        }

        // Lock/unlock icon overlay
        String statusIcon = isUnlocked ? "✓" : (canUnlock ? "🔓" : "🔒");
        int iconX = x + CARD_WIDTH - 12;
        int iconY = y + CARD_HEIGHT - 15;
        int iconColor = isUnlocked ? 0xFF00FF00 : (canUnlock ? 0xFF35A7FF : 0xFF666666);
        context.drawTextWithShadow(this.textRenderer, statusIcon, iconX, iconY, iconColor);
    }

    private String getProductTypeIcon(ProductType type) {
        switch (type) {
            case SEED: return "🌱";
            case FOOD: return "🥕";
            case TOOL: return "🔧";
            case UPGRADE: return "⬆️";
            case UNLOCK: return "🔓";
            default: return "📦";
        }
    }

    private int getProductTypeColor(ProductType type) {
        switch (type) {
            case SEED: return 0xFF228B22; // Forest green
            case FOOD: return 0xFFFF8C00; // Dark orange
            case TOOL: return 0xFF4682B4; // Steel blue
            case UPGRADE: return 0xFFFF6B35; // Orange-red for upgrades
            case UNLOCK: return 0xFF35A7FF; // Bright blue for unlocks
            default: return 0xFF666666; // Gray
        }
    }



    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        if (button == 0) { // Left click
            // Check category tab clicks
            for (ShopCategory category : categories) {
                if (category.isMouseOver((int)mouseX, (int)mouseY)) {
                    selectedCategory = category;
                    scrollOffset = 0; // Reset scroll when switching categories
                    updateFilteredProducts(); // Update products for new category
                    enforceScrollBounds(); // Ensure scroll is within bounds
                    return true;
                }
            }

            // Check product card clicks - return true if handled
            if (handleProductCardClicks(mouseX, mouseY)) {
                return true; // Event was handled, don't propagate further
            }
        }

        return super.mouseClicked(mouseX, mouseY, button);
    }

    private boolean handleProductCardClicks(double mouseX, double mouseY) {
        // Skip if not in product view or no products
        if (selectedCategory == null || selectedCategory.getId().equals("stats") || filteredProducts.isEmpty()) {
            return false;
        }

        // Calculate panel position - ensure it always fits on screen (same as render method)
        int leftX = Math.max(10, Math.min((width - panelWidth) / 2, width - panelWidth - 10));
        int topY = Math.max(10, Math.min((height - panelHeight) / 2, height - panelHeight - 10));

        // Account for timer height (same as in render method)
        int timerHeight = 0;
        if (selectedCategory != null && selectedCategory.getId().equals("food")) {
            timerHeight = 16; // Height for timer display
        }

        int gridX = leftX + SPACING_SM;
        int gridY = topY + HEADER_HEIGHT + TAB_HEIGHT + SPACING_XS + timerHeight;
        int gridWidth = panelWidth - SPACING_SM * 2;

        // Special handling for upgrades category (with sections) - same as rendering
        if (selectedCategory != null && selectedCategory.getId().equals("upgrades")) {
            return handleUpgradesCardClicks(mouseX, mouseY, gridX, gridY, gridWidth);
        }

        // Calculate card layout (same as drawProductGrid)
        int actualCardWidth = CARD_WIDTH;
        int actualCardSpacing = CARD_SPACING;

        // Use the same calculation as drawProductGrid
        cardsPerRow = Math.max(1, Math.min(5, (gridWidth + actualCardSpacing) / (actualCardWidth + actualCardSpacing)));

        int totalCardsWidth = cardsPerRow * actualCardWidth + (cardsPerRow - 1) * actualCardSpacing;
        int startX = gridX + (gridWidth - totalCardsWidth) / 2;

        // Use the same row-based layout as drawProductGrid
        // Calculate total items including "Add New Product" card when in edit mode
        int totalItems = filteredProducts.size();
        if (isEditModeEnabled()) {
            totalItems += 1; // Add one for the "Add New Product" card
        }
        int totalRows = (totalItems + cardsPerRow - 1) / cardsPerRow;
        int currentY = gridY - scrollOffset;

        // Calculate row height including space for modify button when in edit mode (same as drawProductGrid)
        int buttonSpace = isEditModeEnabled() ? 22 : 0; // 18px button + 2px gap + 2px margin
        int rowHeight = CARD_HEIGHT + buttonSpace + actualCardSpacing;

        int cardIndex = 0;
        for (int row = 0; row < totalRows; row++) {
            int rowY = currentY + row * rowHeight;

            for (int col = 0; col < cardsPerRow; col++) {
                int cardX = startX + col * (actualCardWidth + actualCardSpacing);

                // Check if we should handle a product card or the "Add New" card
                if (cardIndex < filteredProducts.size()) {
                    // Handle regular product card
                    Product product = filteredProducts.get(cardIndex);

                    // Enhanced debugging for click detection
                    boolean editModeEnabled = isEditModeEnabled();
                    if (editModeEnabled) {
                        Pokecobbleclaim.LOGGER.debug("Checking clicks for product: {} at cardX={}, rowY={}, mouseX={}, mouseY={}",
                            product.getName(), cardX, rowY, mouseX, mouseY);
                    }

                    // Check for modify button click first (highest priority) - button is below card
                    if (editModeEnabled && isModifyButtonClicked(mouseX, mouseY, cardX, rowY)) {
                        Pokecobbleclaim.LOGGER.info("✓ MODIFY BUTTON CLICKED for product: {} - Opening edit screen", product.getName());
                        openProductEditScreen(product);
                        return true; // Modify button click handled
                    }

                    // Check if click is within the card area (not including button)
                    if (mouseX >= cardX && mouseX <= cardX + CARD_WIDTH &&
                        mouseY >= rowY && mouseY <= rowY + CARD_HEIGHT) {

                        Pokecobbleclaim.LOGGER.info("✓ CARD CLICKED for product: {} - Opening purchase/sell screen", product.getName());
                        // Handle regular product click (works in both edit and normal mode)
                        handleProductClick(product);
                        return true; // Product click handled
                    }
                    cardIndex++;
                } else if (isEditModeEnabled() && cardIndex == filteredProducts.size()) {
                    // Handle "Add New Product" card click
                    if (mouseX >= cardX && mouseX <= cardX + CARD_WIDTH &&
                        mouseY >= rowY && mouseY <= rowY + CARD_HEIGHT) {

                        Pokecobbleclaim.LOGGER.info("✓ ADD NEW PRODUCT CARD CLICKED - Opening product creation screen");
                        openProductCreationScreen();
                        return true; // Add new product click handled
                    }
                    cardIndex++; // Increment to prevent multiple add cards
                } else {
                    // No more cards to handle
                    break;
                }
            }
        }

        return false; // No click was handled
    }

    /**
     * Handles clicks for the upgrades category with sections (same layout as drawUpgradesWithSections).
     */
    private boolean handleUpgradesCardClicks(double mouseX, double mouseY, int x, int y, int width) {
        if (filteredProducts.isEmpty()) {
            return false;
        }

        // Calculate available height for the grid area (same as rendering)
        int gridHeight = panelHeight - HEADER_HEIGHT - TAB_HEIGHT - SPACING_SM - SPACING_XS;

        // Separate products into upgrades and unlocks (same as drawUpgradesWithSections)
        List<Product> upgrades = new ArrayList<>();
        List<Product> unlocks = new ArrayList<>();

        for (Product product : filteredProducts) {
            if (product.getType() == ProductType.UPGRADE) {
                upgrades.add(product);
            } else if (product.getType() == ProductType.UNLOCK) {
                unlocks.add(product);
            }
        }

        int currentY = y - scrollOffset;
        int sectionSpacing = SPACING_MD;

        // Handle Upgrades section clicks
        if (!upgrades.isEmpty()) {
            currentY += 16; // Header height (same as drawUpgradesWithSections)

            // Check clicks in upgrades section
            if (handleProductSectionClicks(mouseX, mouseY, x, currentY, width, gridHeight, upgrades)) {
                return true; // Click was handled
            }

            // Calculate upgrades section height (same as drawUpgradesWithSections)
            int actualCardWidth = CARD_WIDTH;
            int actualCardSpacing = CARD_SPACING;
            int cardsPerRow = Math.max(1, (width - SPACING_SM * 2) / (actualCardWidth + actualCardSpacing));
            int buttonSpace = isEditModeEnabled() ? 22 : 0; // 18px button + 2px gap + 2px margin
            int rowHeight = CARD_HEIGHT + buttonSpace + actualCardSpacing;
            int totalRows = (upgrades.size() + cardsPerRow - 1) / cardsPerRow;
            int upgradesHeight = totalRows * rowHeight;

            currentY += upgradesHeight + sectionSpacing;
        }

        // Handle Unlock Tools section clicks
        if (!unlocks.isEmpty()) {
            currentY += 16; // Header height (same as drawUpgradesWithSections)

            // Check clicks in unlocks section
            return handleProductSectionClicks(mouseX, mouseY, x, currentY, width, gridHeight, unlocks);
        }

        return false; // No click was handled
    }

    /**
     * Handles clicks for a product section (same layout as drawProductSection).
     */
    private boolean handleProductSectionClicks(double mouseX, double mouseY, int x, int y, int width, int height, List<Product> products) {
        if (products.isEmpty()) {
            return false;
        }

        // Calculate grid layout (same as drawProductSection)
        int actualCardWidth = CARD_WIDTH;
        int actualCardSpacing = CARD_SPACING;
        int cardsPerRow = Math.max(1, (width - SPACING_SM * 2) / (actualCardWidth + actualCardSpacing));

        int currentX = x + SPACING_SM;
        int currentY = y;
        // Calculate row height including space for modify button when in edit mode
        int buttonSpace = isEditModeEnabled() ? 22 : 0; // 18px button + 2px gap + 2px margin
        int rowHeight = CARD_HEIGHT + buttonSpace + actualCardSpacing;
        int cardsInCurrentRow = 0;

        for (Product product : products) {
            // Check if card is visible (same culling logic as drawProductSection)
            if (currentY + CARD_HEIGHT < 0 || currentY > height + scrollOffset) {
                // Skip click detection but still update position (same as rendering)
                cardsInCurrentRow++;
                if (cardsInCurrentRow >= cardsPerRow) {
                    cardsInCurrentRow = 0;
                    currentX = x + SPACING_SM;
                    currentY += rowHeight;
                } else {
                    currentX += actualCardWidth + actualCardSpacing;
                }
                continue;
            }

            // Enhanced debugging for click detection
            boolean editModeEnabled = isEditModeEnabled();
            if (editModeEnabled) {
                Pokecobbleclaim.LOGGER.debug("Checking section clicks for product: {} at currentX={}, currentY={}, mouseX={}, mouseY={}",
                    product.getName(), currentX, currentY, mouseX, mouseY);
            }

            // Check for modify button click first (highest priority) - button is below card
            if (editModeEnabled && isModifyButtonClicked(mouseX, mouseY, currentX, currentY)) {
                Pokecobbleclaim.LOGGER.info("✓ MODIFY BUTTON CLICKED for product: {} - Opening edit screen", product.getName());
                openProductEditScreen(product);
                return true; // Modify button click handled
            }

            // Check if click is within the card area (not including button)
            if (mouseX >= currentX && mouseX <= currentX + CARD_WIDTH &&
                mouseY >= currentY && mouseY <= currentY + CARD_HEIGHT) {

                Pokecobbleclaim.LOGGER.info("✓ CARD CLICKED for product: {} - Opening purchase/sell screen", product.getName());
                // Handle regular product click (works in both edit and normal mode)
                handleProductClick(product);
                return true; // Product click handled
            }

            // Update position for next card (same as drawProductSection)
            cardsInCurrentRow++;
            if (cardsInCurrentRow >= cardsPerRow) {
                cardsInCurrentRow = 0;
                currentX = x + SPACING_SM;
                currentY += rowHeight;
            } else {
                currentX += actualCardWidth + actualCardSpacing;
            }
        }

        return false; // No click was handled
    }

    private void handleProductClick(Product product) {
        Pokecobbleclaim.LOGGER.info("🛒 handleProductClick called for product: {} (type: {})",
            product.getName(), product.getType());

        // Handle different product types
        switch (product.getType()) {
            case FOOD:
                // Open food selling screen
                if (selectedCategory.getId().equals("food")) {
                    openFoodSellScreen(product);
                }
                break;
            case SEED:
                // Handle seed purchase
                if (selectedCategory.getId().equals("seeds")) {
                    handleSeedPurchase(product);
                }
                break;
            case TOOL:
                // Handle tool purchase
                if (selectedCategory.getId().equals("tools")) {
                    handleToolPurchase(product);
                }
                break;
            case UPGRADE:
                // Handle upgrade purchase
                if (selectedCategory.getId().equals("upgrades")) {
                    handleUpgradePurchase(product);
                }
                break;
            case UNLOCK:
                // Handle tool unlock (now in upgrades category)
                if (selectedCategory.getId().equals("upgrades")) {
                    handleToolUnlock(product);
                }
                break;
        }
    }

    // Gamification helper methods

    /**
     * Gets the current upgrade level for a specific upgrade.
     */
    private int getCurrentUpgradeLevel(String upgradeId) {
        // TODO: Implement actual upgrade level tracking
        // For now, return mock data based on upgrade ID
        switch (upgradeId) {
            case "seed_discount": return 2; // Level 2/5
            case "sell_bonus": return 0; // Not purchased
            case "tool_discount": return 5; // Maxed out
            default: return 0;
        }
    }



    /**
     * Checks if a tool is already unlocked.
     */
    private boolean isToolUnlocked(String toolId) {
        // TODO: Implement actual unlock tracking
        // For now, return mock data
        switch (toolId) {
            case "unlock_sprinkler": return true; // Already unlocked
            case "unlock_harvester": return false; // Not unlocked
            case "unlock_fertilizer": return false; // Not unlocked
            default: return false;
        }
    }

    /**
     * Checks if a tool can be unlocked (meets requirements).
     */
    private boolean canUnlockTool(String toolId) {
        // TODO: Implement actual requirement checking
        // For now, return mock data based on player level/progress
        switch (toolId) {
            case "unlock_sprinkler": return true; // Already unlocked
            case "unlock_harvester": return true; // Can unlock (meets requirements)
            case "unlock_fertilizer": return false; // Cannot unlock (missing requirements)
            default: return false;
        }
    }

    /**
     * Gets the effect description for an upgrade.
     */
    private String getUpgradeEffect(String upgradeId) {
        switch (upgradeId) {
            case "seed_discount": return "Next: -20% seed costs";
            case "sell_bonus": return "Next: +25% sell prices";
            case "tool_discount": return "Next: -30% tool costs";
            default: return "Unknown effect";
        }
    }

    /**
     * Gets the unlock requirement text for a tool.
     */
    private String getUnlockRequirement(String toolId) {
        switch (toolId) {
            case "unlock_sprinkler": return "Level 2 Required";
            case "unlock_harvester": return "Level 4 Required";
            case "unlock_fertilizer": return "100 Harvests Needed";
            default: return "Requirements Unknown";
        }
    }



    private void openFoodSellScreen(Product product) {
        // Basic validation
        if (this.client == null || this.client.player == null || product.getItemStack() == null) {
            return;
        }

        // Always open the sell screen - let it handle inventory validation server-side
        // This improves UX by allowing players to see the sell interface even with no items
        FoodSellScreen sellScreen = new FoodSellScreen(this, product);
        this.client.setScreen(sellScreen);

        Pokecobbleclaim.LOGGER.debug("Opened sell screen for {} - server will validate inventory", product.getName());
    }

    private void handleSeedPurchase(Product product) {
        // Open purchase screen for seeds
        ProductPurchaseScreen purchaseScreen = new ProductPurchaseScreen(this, product);
        this.client.setScreen(purchaseScreen);
        Pokecobbleclaim.LOGGER.info("Opening purchase screen for seed: {}", product.getName());
    }

    private void handleToolPurchase(Product product) {
        // Open purchase screen for tools
        ProductPurchaseScreen purchaseScreen = new ProductPurchaseScreen(this, product);
        this.client.setScreen(purchaseScreen);
        Pokecobbleclaim.LOGGER.info("Opening purchase screen for tool: {}", product.getName());
    }

    private void handleUpgradePurchase(Product product) {
        // Open purchase screen for upgrades
        ProductPurchaseScreen purchaseScreen = new ProductPurchaseScreen(this, product);
        this.client.setScreen(purchaseScreen);
        Pokecobbleclaim.LOGGER.info("Opening purchase screen for upgrade: {}", product.getName());
    }

    private void handleToolUnlock(Product product) {
        // Open purchase screen for unlocks
        ProductPurchaseScreen purchaseScreen = new ProductPurchaseScreen(this, product);
        this.client.setScreen(purchaseScreen);
        Pokecobbleclaim.LOGGER.info("Opening purchase screen for unlock: {}", product.getName());
    }

    /**
     * Draws the price update countdown timer for the food category.
     */
    private void drawPriceUpdateTimer(DrawContext context, int x, int y, int width) {
        // Update timer text if needed
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastTimerUpdate >= TIMER_UPDATE_INTERVAL) {
            cachedTimerText = getTimerText();
            lastTimerUpdate = currentTime;
        }

        // Draw timer background
        int timerY = y + 2;
        int timerHeight = 12;
        context.fill(x + SPACING_SM, timerY, x + width - SPACING_SM, timerY + timerHeight, 0x80000000);

        // Draw timer text
        String timerText = cachedTimerText;
        int textWidth = this.textRenderer.getWidth(timerText);
        int textX = x + (width - textWidth) / 2;
        int textY = timerY + 2;

        context.drawTextWithShadow(this.textRenderer, timerText, textX, textY, TEXT_ACCENT);
    }

    /**
     * Gets the current timer text based on time until next price update.
     */
    private String getTimerText() {
        try {
            // Get timer info from client-side timer manager
            var clientTimer = com.pokecobble.phone.food.client.ClientFoodPriceTimer.getInstance();

            // Use fallback method which handles all cases including requesting data
            return clientTimer.getFallbackTimerText();

        } catch (Exception e) {
            return "§6Next Price Update: §fUnavailable";
        }
    }

    /**
     * Gets the current dynamic price for a product, falling back to static price if unavailable.
     */
    private int getCurrentDynamicPrice(Product product) {
        try {
            // Only get dynamic prices for food items
            if (product.getType() == ProductType.FOOD) {
                var clientPriceManager = com.pokecobble.phone.food.client.ClientFoodPriceManager.getInstance();
                int serverPrice = clientPriceManager.getCurrentPrice(product.getId());

                // Use server price if available, otherwise fall back to product price
                return serverPrice > 0 ? serverPrice : product.getPrice();
            }

            // For non-food items, use static price
            return product.getPrice();

        } catch (Exception e) {
            // Fall back to static product price if client manager is not available
            return product.getPrice();
        }
    }

    /**
     * Gets the formatted balance string for display.
     */
    private String getFormattedBalance() {
        try {
            // Get balance from new economy system
            com.pokecobble.economy.api.EconomyAPI economyAPI = com.pokecobble.economy.api.EconomyAPI.getInstance();
            Pokecobbleclaim.LOGGER.debug("FarmerApp: EconomyAPI available: " + economyAPI.isAvailable());

            if (economyAPI.isAvailable() && this.client != null && this.client.player != null) {
                UUID playerId = this.client.player.getUuid();
                long balance = economyAPI.getBalance(playerId);
                String currencySymbol = economyAPI.getCurrencySymbol();

                Pokecobbleclaim.LOGGER.debug("FarmerApp: Player " + playerId + " balance: " + balance + ", currency: " + currencySymbol);

                return String.format("%,d %s", balance, currencySymbol);
            } else {
                // Economy system not available
                Pokecobbleclaim.LOGGER.debug("FarmerApp: Economy system not available or player is null");
                return "0 💰";
            }
        } catch (Exception e) {
            // Error getting balance
            Pokecobbleclaim.LOGGER.error("FarmerApp: Error getting balance: " + e.getMessage(), e);
            return "0 💰";
        }
    }

    /**
     * Sets up the price update listener to refresh the screen when prices change.
     */
    private void setupPriceUpdateListener() {
        try {
            var clientPriceManager = com.pokecobble.phone.food.client.ClientFoodPriceManager.getInstance();

            // Create listener that refreshes the screen when prices update
            priceUpdateListener = (updatedPrices) -> {
                // Only refresh if we're currently showing food items
                if (selectedCategory != null && selectedCategory.getId().equals("food")) {
                    // Force a screen refresh by clearing cached data
                    cachedTimerText = "";
                    lastTimerUpdate = 0;
                }
            };

            // Register the listener
            clientPriceManager.addPriceUpdateListener(priceUpdateListener);

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.warn("Failed to setup price update listener: " + e.getMessage());
        }
    }

    /**
     * Requests fresh price data from server if the client doesn't have current data.
     */
    private void requestPriceDataIfNeeded() {
        try {
            var clientPriceManager = com.pokecobble.phone.food.client.ClientFoodPriceManager.getInstance();

            // Check if we have price data
            if (!clientPriceManager.hasPriceData()) {
                // Request fresh data from server
                clientPriceManager.requestPriceUpdate();
                Pokecobbleclaim.LOGGER.debug("Requested fresh price data for farmer app");
            }

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.warn("Failed to request price data: " + e.getMessage());
        }
    }

    /**
     * Requests fresh product data from server if the client doesn't have current data.
     */
    private void requestProductDataIfNeeded() {
        try {
            var clientProductManager = com.pokecobble.phone.client.ClientProductManager.getInstance();

            // Check if we have product data
            if (clientProductManager.getAllProducts().isEmpty()) {
                // Request fresh data from server
                clientProductManager.requestFullSync();
                Pokecobbleclaim.LOGGER.debug("Requested fresh product data for farmer app");
            }

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.warn("Failed to request product data: " + e.getMessage());
        }

        // Request balance from economy system
        try {
            net.minecraft.network.PacketByteBuf buf = net.fabricmc.fabric.api.networking.v1.PacketByteBufs.create();
            net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking.send(
                com.pokecobble.economy.network.EconomyNetworkManager.ECONOMY_BALANCE_REQUEST, buf
            );
            Pokecobbleclaim.LOGGER.info("FarmerApp: Sent balance request to economy system");
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("FarmerApp: Failed to request balance from economy system", e);
        }
    }

    /**
     * Cleans up the price update listener when the screen is closed.
     */
    private void cleanupPriceUpdateListener() {
        if (priceUpdateListener != null) {
            try {
                var clientPriceManager = com.pokecobble.phone.food.client.ClientFoodPriceManager.getInstance();
                clientPriceManager.removePriceUpdateListener(priceUpdateListener);
                priceUpdateListener = null;
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.warn("Failed to cleanup price update listener: " + e.getMessage());
            }
        }
    }



    @Override
    public boolean mouseScrolled(double mouseX, double mouseY, double amount) {
        int maxScroll = calculateMaxScroll();

        // Apply scroll with bounds checking
        scrollOffset -= (int)(amount * SCROLL_AMOUNT);
        scrollOffset = Math.max(0, Math.min(scrollOffset, maxScroll));

        return true;
    }

    /**
     * Calculates the maximum scroll offset based on current category and content.
     */
    private int calculateMaxScroll() {
        if (selectedCategory == null) {
            return 0;
        }

        // Calculate available height for content
        int availableHeight = panelHeight - HEADER_HEIGHT - TAB_HEIGHT - SPACING_SM - SPACING_XS;

        switch (selectedCategory.getId()) {
            case "stats":
                return calculateStatsMaxScroll(availableHeight);
            case "upgrades":
                return calculateUpgradesMaxScroll(availableHeight);
            case "seeds":
            case "food":
            case "tools":
                return calculateProductsMaxScroll(availableHeight);
            default:
                return 0;
        }
    }

    /**
     * Calculates max scroll for statistics category.
     */
    private int calculateStatsMaxScroll(int availableHeight) {
        // Statistics cards configuration
        int statCardHeight = 50; // Match the actual card height used in drawStatistics
        int statCardSpacing = SPACING_SM;
        int totalStatCards = 4; // Farming Level, Total Harvests, Coins Earned, Experience

        int totalStatsHeight = totalStatCards * (statCardHeight + statCardSpacing);
        return Math.max(0, totalStatsHeight - availableHeight);
    }

    /**
     * Calculates max scroll for upgrades category with sections.
     */
    private int calculateUpgradesMaxScroll(int availableHeight) {
        if (filteredProducts.isEmpty()) {
            return 0;
        }

        // Separate products into upgrades and unlocks
        List<Product> upgrades = new ArrayList<>();
        List<Product> unlocks = new ArrayList<>();

        for (Product product : filteredProducts) {
            if (product.getType() == ProductType.UPGRADE) {
                upgrades.add(product);
            } else if (product.getType() == ProductType.UNLOCK) {
                unlocks.add(product);
            }
        }

        int totalHeight = 0;
        int sectionSpacing = SPACING_MD;

        // Calculate row height including space for modify button when in edit mode
        int buttonSpace = isEditModeEnabled() ? 22 : 0; // 18px button + 2px gap + 2px margin
        int rowHeight = CARD_HEIGHT + buttonSpace + CARD_SPACING;

        // Calculate height for upgrades section
        if (!upgrades.isEmpty()) {
            totalHeight += 16; // Section header height
            int upgradesRows = (upgrades.size() + cardsPerRow - 1) / cardsPerRow;
            totalHeight += upgradesRows * rowHeight;
            totalHeight += sectionSpacing;
        }

        // Calculate height for unlocks section
        if (!unlocks.isEmpty()) {
            totalHeight += 16; // Section header height
            int unlocksRows = (unlocks.size() + cardsPerRow - 1) / cardsPerRow;
            totalHeight += unlocksRows * rowHeight;
        }

        return Math.max(0, totalHeight - availableHeight);
    }

    /**
     * Calculates max scroll for product categories.
     */
    private int calculateProductsMaxScroll(int availableHeight) {
        if (filteredProducts.isEmpty()) {
            return 0;
        }

        // Account for timer height in food category
        int timerHeight = 0;
        if (selectedCategory != null && selectedCategory.getId().equals("food")) {
            timerHeight = 16; // Height for timer display
        }

        // Adjust available height for timer
        availableHeight -= timerHeight;

        // Calculate grid dimensions including space for modify button when in edit mode
        // Calculate total items including "Add New Product" card when in edit mode
        int totalItems = filteredProducts.size();
        if (isEditModeEnabled()) {
            totalItems += 1; // Add one for the "Add New Product" card
        }
        int totalRows = (totalItems + cardsPerRow - 1) / cardsPerRow;

        // Calculate row height including space for modify button when in edit mode
        int buttonSpace = isEditModeEnabled() ? 22 : 0; // 18px button + 2px gap + 2px margin
        int rowHeight = CARD_HEIGHT + buttonSpace + CARD_SPACING;

        int totalGridHeight = totalRows * rowHeight;

        return Math.max(0, totalGridHeight - availableHeight);
    }

    /**
     * Ensures scroll offset is within valid bounds for current content.
     */
    private void enforceScrollBounds() {
        int maxScroll = calculateMaxScroll();
        scrollOffset = Math.max(0, Math.min(scrollOffset, maxScroll));
    }

    /**
     * Smoothly adjusts scroll position when screen size changes to maintain relative position.
     * This prevents jarring jumps when the user resizes the screen.
     */
    private void adjustScrollForResize() {
        int maxScroll = calculateMaxScroll();

        // If there's no scrollable content, reset to top
        if (maxScroll <= 0) {
            scrollOffset = 0;
            return;
        }

        // Try to maintain the same relative scroll position
        // This helps keep the user's view context when resizing
        if (scrollOffset > maxScroll) {
            // If current scroll exceeds new max, smoothly adjust to max
            scrollOffset = maxScroll;
        }

        // Ensure bounds are still valid
        enforceScrollBounds();
    }

    /**
     * Draws a scroll indicator showing current scroll position.
     */
    private void drawScrollIndicator(DrawContext context, int x, int y, int height) {
        int maxScroll = calculateMaxScroll();

        // Only draw if content is scrollable
        if (maxScroll <= 0) {
            return;
        }

        // Calculate scroll indicator dimensions
        int indicatorWidth = 4;
        int indicatorHeight = Math.max(20, (height * height) / (height + maxScroll));
        int indicatorY = y + (int)((double)scrollOffset / maxScroll * (height - indicatorHeight));

        // Draw scroll track (background)
        context.fill(x, y, x + indicatorWidth, y + height, 0x40000000);

        // Draw scroll thumb (indicator)
        int thumbColor = 0x80FFFFFF;
        context.fill(x, indicatorY, x + indicatorWidth, indicatorY + indicatorHeight, thumbColor);

        // Add subtle highlight to thumb
        context.fill(x, indicatorY, x + 1, indicatorY + indicatorHeight, 0x40FFFFFF);
    }

    @Override
    public boolean keyPressed(int keyCode, int scanCode, int modifiers) {
        // Handle keyboard scrolling
        if (keyCode == 265) { // Up arrow
            scrollOffset -= SCROLL_AMOUNT;
            enforceScrollBounds();
            return true;
        } else if (keyCode == 264) { // Down arrow
            scrollOffset += SCROLL_AMOUNT;
            enforceScrollBounds();
            return true;
        } else if (keyCode == 266) { // Page Up
            scrollOffset -= SCROLL_AMOUNT * 3;
            enforceScrollBounds();
            return true;
        } else if (keyCode == 267) { // Page Down
            scrollOffset += SCROLL_AMOUNT * 3;
            enforceScrollBounds();
            return true;
        } else if (keyCode == 268) { // Home
            scrollOffset = 0;
            return true;
        } else if (keyCode == 269) { // End
            scrollOffset = calculateMaxScroll();
            return true;
        }

        // Handle search input
        if (keyCode == 259) { // Backspace
            if (!searchQuery.isEmpty()) {
                searchQuery = searchQuery.substring(0, searchQuery.length() - 1);
                updateFilteredProducts();
                return true;
            }
        } else if (keyCode == 257 || keyCode == 335) { // Enter or Numpad Enter
            // Could trigger search or product selection
            return true;
        }
        return super.keyPressed(keyCode, scanCode, modifiers);
    }

    @Override
    public boolean charTyped(char chr, int modifiers) {
        // Add character to search query
        if (Character.isLetterOrDigit(chr) || chr == ' ') {
            if (searchQuery.length() < 20) { // Limit search length
                searchQuery += chr;
                updateFilteredProducts();
                return true;
            }
        }
        return super.charTyped(chr, modifiers);
    }

    @Override
    public void close() {
        cleanupPriceUpdateListener();
        this.client.setScreen(parent);
    }

    @Override
    public boolean shouldPause() {
        return false;
    }

    @Override
    public void resize(net.minecraft.client.MinecraftClient client, int width, int height) {
        super.resize(client, width, height);

        // CRITICAL: Recalculate panel dimensions when screen resizes
        // This is essential for all click zones to work correctly
        calculateResponsiveDimensions();

        // Smoothly adjust scroll position to maintain user context
        adjustScrollForResize();

        Pokecobbleclaim.LOGGER.debug("FarmerAppScreen resized to {}x{}, panel: {}x{}, cards per row: {}, visible rows: {}",
            width, height, panelWidth, panelHeight, cardsPerRow, visibleRows);
    }

    /**
     * Enum for product types
     */
    public enum ProductType {
        SEED, FOOD, TOOL, UPGRADE, UNLOCK
    }

    /**
     * Class representing a shop category
     */
    public static class ShopCategory {
        private final String id;
        private final String name;
        private final String description;
        private final int color;
        private int x, y, width, height;

        public ShopCategory(String id, String name, String description, int color) {
            this.id = id;
            this.name = name;
            this.description = description;
            this.color = color;
        }

        public void setPosition(int x, int y, int width, int height) {
            this.x = x;
            this.y = y;
            this.width = width;
            this.height = height;
        }

        public boolean isMouseOver(int mouseX, int mouseY) {
            return mouseX >= x && mouseX <= x + width && mouseY >= y && mouseY <= y + height;
        }

        // Getters
        public String getId() { return id; }
        public String getName() { return name; }
        public String getDescription() { return description; }
        public int getColor() { return color; }
        public int getX() { return x; }
        public int getY() { return y; }
        public int getWidth() { return width; }
        public int getHeight() { return height; }
    }

    /**
     * Class representing a product in the farmer shop
     */
    public static class Product {
        private final String id;
        private final String name;
        private final String description;
        private final String iconPath;
        private final ItemStack itemStack;
        private final int price;
        private final ProductType type;
        private final int requiredLevel;
        private boolean isOnSale = false;
        private int discountPercent = 0;

        // Constructor for texture-based products (tools)
        public Product(String id, String name, String description, String iconPath,
                      int price, ProductType type, int requiredLevel) {
            this.id = id;
            this.name = name;
            this.description = description;
            this.iconPath = iconPath;
            this.itemStack = null;
            this.price = price;
            this.type = type;
            this.requiredLevel = requiredLevel;
        }

        // Constructor for ItemStack-based products (seeds and food)
        public Product(String id, String name, String description, ItemStack itemStack,
                      int price, ProductType type, int requiredLevel) {
            this.id = id;
            this.name = name;
            this.description = description;
            this.iconPath = null;
            this.itemStack = itemStack;
            this.price = price;
            this.type = type;
            this.requiredLevel = requiredLevel;
        }

        public void setOnSale(boolean onSale, int discountPercent) {
            this.isOnSale = onSale;
            this.discountPercent = discountPercent;
        }

        public int getDiscountedPrice() {
            if (isOnSale) {
                return price - (price * discountPercent / 100);
            }
            return price;
        }

        // Getters
        public String getId() { return id; }
        public String getName() { return name; }
        public String getDescription() { return description; }
        public String getIconPath() { return iconPath; }
        public ItemStack getItemStack() { return itemStack; }
        public int getPrice() { return price; }
        public ProductType getType() { return type; }
        public int getRequiredLevel() { return requiredLevel; }
        public boolean isOnSale() { return isOnSale; }
        public int getDiscountPercent() { return discountPercent; }
        public boolean hasItemStack() { return itemStack != null; }
    }

    /**
     * Refreshes the product data from the ClientProductManager cache.
     * This method should be called when product data is updated on the server.
     */
    public void refreshProductData() {
        try {
            Pokecobbleclaim.LOGGER.debug("Refreshing FarmerAppScreen product data...");

            // Get updated products from ClientProductManager
            var clientProductManager = com.pokecobble.phone.client.ClientProductManager.getInstance();
            var updatedProducts = clientProductManager.getAllProducts();

            // Clear current products and add updated ones
            allProducts.clear();
            for (var product : updatedProducts.values()) {
                allProducts.add(product);
            }

            // Update filtered products based on current category
            updateFilteredProducts();

            // Reset scroll position to prevent issues
            scrollOffset = 0;

            Pokecobbleclaim.LOGGER.debug("Refreshed FarmerAppScreen with {} products", allProducts.size());

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to refresh FarmerAppScreen product data", e);
        }
    }

    /**
     * Updates a specific product in the screen's product list.
     */
    public void updateProduct(String productId, FarmerAppScreen.Product updatedProduct) {
        try {
            // Find and update the product in allProducts
            for (int i = 0; i < allProducts.size(); i++) {
                if (allProducts.get(i).getId().equals(productId)) {
                    allProducts.set(i, updatedProduct);
                    break;
                }
            }

            // Update filtered products
            updateFilteredProducts();

            Pokecobbleclaim.LOGGER.debug("Updated product {} in FarmerAppScreen", productId);

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to update product {} in FarmerAppScreen", productId, e);
        }
    }
}
